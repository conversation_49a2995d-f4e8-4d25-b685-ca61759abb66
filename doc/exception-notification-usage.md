# 异常通知功能使用说明

## 功能概述

异常通知功能可以在系统发生不可预知的异常时，自动将异常信息发送到飞书群，帮助开发团队及时发现和处理问题。

## 主要特性

1. **异步处理**：异常通知采用异步方式发送，不会影响正常的响应时间
2. **自动捕获**：全局异常处理器会自动捕获未处理的异常并发送通知
3. **详细信息**：通知包含异常类型、消息、堆栈信息、请求URL、客户端IP等详细信息
4. **容错机制**：通知服务本身的异常不会影响主业务流程
5. **Grafana集成**：支持一键跳转到Grafana查看详细日志

## 配置说明

### 1. 飞书机器人配置

在 `application-local.yaml` 中配置飞书机器人信息：

```yaml
feishu:
  bot:
    # 飞书机器人Webhook地址
    webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url
    # 飞书机器人密钥（用于签名验证）
    secret: your-secret
    # 是否启用飞书机器人
    enabled: true
    # 请求超时时间（毫秒）
    timeout: 10000
    # Grafana基础URL（用于错误通知中的日志查看链接）
    grafana-base-url: http://************:31588
```

### 2. 异步虚拟线程配置

系统已自动配置了专用的虚拟线程执行器：

- 核心线程数：1个
- 最大线程数：20个
- 队列容量：500个
- 线程类型：虚拟线程（Virtual Threads）
- 线程名前缀：`exception-notification-vt-`
- 线程本地变量：不继承（避免内存泄漏）

**虚拟线程优势：**
- 更低的资源消耗：虚拟线程比传统线程占用更少的内存
- 更高的并发能力：可以创建数百万个虚拟线程
- 更适合I/O密集型任务：如网络请求、文件操作等
- 自动调度：由JVM自动管理，无需手动优化

## 使用方法

### 1. 自动异常通知

当系统发生未处理的异常时，全局异常处理器会自动发送通知到飞书群。

### 2. 手动发送异常通知

在业务代码中可以手动调用异常通知服务：

```java
@Autowired
private FeishuBotService feishuBotService;

// 发送简单异常通知
feishuBotService.sendErrorNotification(
    exception.getClass().getSimpleName() + ": " + exception.getMessage(),
    "业务上下文信息"
);

// 发送带Grafana链接的异常通知
feishuBotService.sendErrorNotificationWithGrafanaButton(
    exception.getClass().getSimpleName() + ": " + exception.getMessage(),
    "业务上下文信息",
    "request-id-12345"
);
```

### 3. 线程池管理（推荐）

使用虚拟线程池确保异常通知能够完成执行，避免被主线程终止：

#### 3.1 配置线程池

```java
@Configuration
public class ExceptionNotificationConfig {
    
    @Bean("exceptionNotificationExecutor")
    public ExecutorService exceptionNotificationExecutor() {
        // 创建虚拟线程池，确保线程能够完成执行
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        return executor;
    }
}
```

#### 3.2 使用线程池发送通知

```java
@Autowired
@Qualifier("exceptionNotificationExecutor")
private ExecutorService exceptionNotificationExecutor;

@Autowired
private FeishuBotService feishuBotService;

/**
 * 异步发送异常通知
 */
private void sendExceptionNotificationAsync(Exception exception) {
    // 在虚拟线程启动前获取请求上下文信息
    final ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    final String requestUrl;
    final String requestMethod;
    final String clientIp;
    final String userAgent;
    final String requestId;
    
    if (requestAttributes != null) {
        HttpServletRequest request = requestAttributes.getRequest();
        requestUrl = request.getRequestURL().toString();
        requestMethod = request.getMethod();
        clientIp = getClientIpAddress(request);
        userAgent = request.getHeader("User-Agent");
        requestId = request.getAttribute("requestId") != null ? request.getAttribute("requestId").toString() : "-";
    } else {
        requestUrl = null;
        requestMethod = null;
        clientIp = null;
        userAgent = null;
        requestId = null;
    }
    
    // 使用线程池中的虚拟线程异步发送异常通知，确保能够完成执行
    exceptionNotificationExecutor.submit(() -> {
        try {
            // 构建异常通知内容，使用预先获取的请求信息
            String notificationContent = ExceptionNotificationUtil.buildExceptionNotificationContent(
                exception, requestUrl, requestMethod, userAgent, requestId
            );
            
            // 发送到飞书（带Grafana按钮）
            boolean success = feishuBotService.sendErrorNotificationWithGrafanaButton(
                exception.getClass().getSimpleName() + ": " + exception.getMessage(),
                notificationContent,
                requestId
            );
            
            if (success) {
                log.info("异常通知发送成功");
            } else {
                log.warn("异常通知发送失败");
            }
        } catch (Exception notificationException) {
            log.error("发送异常通知失败", notificationException);
        }
    });
}

// 在异常处理器中使用
@ExceptionHandler(value = Exception.class)
public Response exceptionHandler(Exception e) throws Exception {
    log.error(e.getMessage(), e);
    
    // 异步发送异常通知
    sendExceptionNotificationAsync(e);
    
    return Response.fail("服务器开小差去了(((o(*ﾟ▽ﾟ*)o)))");
}
```

**优势：**
- 代码更简洁，主方法不会过长
- 逻辑封装，便于维护和复用
- 直接使用Java 21虚拟线程
- 更好的性能和可读性

**注意事项：**
- 虚拟线程中无法直接使用 `RequestContextHolder.getRequestAttributes()`
- 需要在虚拟线程启动前获取请求信息并传递
- 使用 `final` 变量确保线程安全
- **重要：使用线程池确保虚拟线程能够完成执行，避免被主线程终止**

### 4. 测试异常通知功能

系统提供了测试接口来验证异常通知功能：

```bash
# 测试基本异常通知
GET /api/example/exception/test

# 测试多种类型异常
GET /api/example/exception/test-different-exceptions

# 虚拟线程性能测试
GET /api/example/virtual-thread/performance-test

# 虚拟线程与平台线程性能对比
GET /api/example/virtual-thread/comparison-test

# 直接虚拟线程测试
GET /api/example/direct-virtual-thread/test

# 业务场景虚拟线程示例
GET /api/example/direct-virtual-thread/business-example

# 虚拟线程请求上下文测试
GET /api/example/request-context/test-virtual-thread

# 请求上下文传递测试
GET /api/example/request-context/test-pass-context
```

## Grafana集成

### 功能说明

异常通知功能集成了Grafana日志查看功能，当系统发生异常时，飞书消息中会包含一个"查看详细日志"按钮，点击后可以直接跳转到Grafana查看该请求ID相关的日志。

### 配置要求

在 `application-local.yaml` 中需要配置Grafana基础URL：

```yaml
feishu:
  bot:
    # 其他配置...
    # Grafana基础URL
    grafana-base-url: http://************:31588
```

### 使用方法

```java
@Autowired
private FeishuBotService feishuBotService;

// 发送带Grafana链接的错误通知
boolean success = feishuBotService.sendErrorNotificationWithGrafanaButton(
    "数据库连接失败", 
    "用户登录时发生数据库连接异常", 
    "request-id-12345"
);
```

### 自动集成

该功能已自动集成到全局异常处理器中，当系统发生异常时会自动发送带Grafana链接的错误通知。

### Grafana链接格式

生成的Grafana链接格式为：
```
http://************:31588/explore?orgId=1&left={"datasource":"ab10533a-937d-40e0-bb29-cb9ff814fe1a","queries":[{"refId":"A","expr":"{app=\"digitalbook\"}|=`{requestId}`","queryType":"range","datasource":{"type":"loki","uid":"ab10533a-937d-40e0-bb29-cb9ff814fe1a"},"editorMode":"builder"}],"range":{"from":"now-24h","to":"now"}}
```

其中 `{requestId}` 会被替换为实际的请求ID。

### 测试Grafana集成

```bash
# 测试带Grafana链接的错误通知
mvn test -Dtest=FeishuBotServiceTest#testSendErrorNotificationWithGrafanaButton
```

## 通知内容格式

飞书通知包含以下信息：

1. **异常类型**：异常的完整类名
2. **异常消息**：异常的具体错误信息
3. **发生时间**：异常发生的时间戳
4. **请求URL**：触发异常的请求地址
5. **请求方法**：HTTP请求方法（GET、POST等）
6. **客户端IP**：请求来源的IP地址
7. **User-Agent**：客户端浏览器信息
8. **请求ID**：用于关联Grafana日志的唯一标识
9. **上下文信息**：业务相关的上下文描述
10. **异常链分析**：显示异常的根本原因
11. **解决建议**：根据异常类型提供针对性建议
12. **详细堆栈**：突出显示项目代码位置
13. **Grafana按钮**：一键查看详细日志

## 注意事项

1. **性能影响**：异常通知采用异步虚拟线程处理，不会影响正常的响应时间
2. **虚拟线程要求**：需要Java 21或更高版本才能使用虚拟线程功能
3. **请求上下文问题**：虚拟线程中无法直接使用 `RequestContextHolder.getRequestAttributes()`，需要在虚拟线程启动前获取请求信息
4. **通知频率**：为避免通知过多，建议在生产环境中合理配置通知策略
5. **信息安全**：通知中可能包含敏感信息，请确保飞书群的安全性
6. **网络依赖**：通知功能依赖网络连接，网络异常时通知可能失败
7. **配置检查**：确保飞书机器人配置正确且已启用
8. **Grafana配置**：确保 `grafana-base-url` 配置正确，否则Grafana链接无法正常工作

## 虚拟线程请求上下文说明

### 问题描述

在虚拟线程中，`RequestContextHolder.getRequestAttributes()` 可能无法正常工作，因为：

1. **线程本地变量继承**：虚拟线程默认不继承父线程的 `InheritableThreadLocal`
2. **请求上下文丢失**：虚拟线程可能无法访问到原始请求的上下文信息

### 解决方案

在虚拟线程启动前获取请求信息并传递：

```java
// 在主线程中获取请求信息
final ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
final String requestUrl;
final String requestMethod;
final String clientIp;
final String userAgent;
final String requestId;

if (requestAttributes != null) {
    HttpServletRequest request = requestAttributes.getRequest();
    requestUrl = request.getRequestURL().toString();
    requestMethod = request.getMethod();
    clientIp = getClientIpAddress(request);
    userAgent = request.getHeader("User-Agent");
    requestId = request.getAttribute("requestId") != null ? request.getAttribute("requestId").toString() : "-";
} else {
    requestUrl = null;
    requestMethod = null;
    clientIp = null;
    userAgent = null;
    requestId = null;
}

// 将信息传递给虚拟线程
Thread.ofVirtual()
    .name("notification-vt-", 0)
    .start(() -> {
        // 使用传递的请求信息
        String content = buildNotificationContent(exception, requestUrl, requestMethod, clientIp, userAgent, requestId);
        // 发送通知...
    });
```

### 测试验证

可以使用以下接口测试虚拟线程中的请求上下文行为：

```bash
# 测试虚拟线程中的请求上下文
GET /api/example/request-context/test-virtual-thread

# 测试传递请求信息到虚拟线程
GET /api/example/request-context/test-pass-context
```

## 故障排查

### 1. 通知未发送

- 检查飞书机器人配置是否正确
- 确认 `feishu.bot.enabled` 为 `true`
- 查看应用日志中的错误信息

### 2. 通知发送失败

- 检查网络连接是否正常
- 确认飞书Webhook地址是否有效
- 查看飞书机器人的权限设置

### 3. 通知内容不完整

- 检查异常对象是否包含完整的堆栈信息
- 确认请求上下文是否可用

### 4. Grafana链接无法访问

- 检查 `grafana-base-url` 配置是否正确
- 确认Grafana服务是否正常运行
- 检查网络连接和防火墙设置

### 5. 异常通知不发送

- 检查 `enabled` 配置是否为 `true`
- 确认飞书机器人服务是否正常注入
- 查看应用日志中的异常信息

## 扩展功能

### 1. 添加其他通知渠道

可以通过实现 `ExceptionNotificationService` 接口来添加其他通知渠道（如钉钉、企业微信等）。

### 2. 自定义通知内容

可以修改 `ExceptionNotificationServiceImpl` 中的 `buildExceptionNotificationContent` 方法来自定义通知内容格式。

### 3. 通知过滤

可以在异常通知服务中添加过滤逻辑，只对特定类型的异常发送通知。

### 4. 虚拟线程监控

可以通过以下方式监控虚拟线程的性能：

```java
// 获取虚拟线程统计信息
ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
long[] threadIds = threadBean.getAllThreadIds();
long virtualThreadCount = Arrays.stream(threadIds)
    .mapToObj(threadBean::getThreadInfo)
    .filter(info -> info != null && info.isVirtual())
    .count();

log.info("当前虚拟线程数量: {}", virtualThreadCount);
```

**监控指标：**
- 虚拟线程创建数量
- 虚拟线程执行时间
- 队列积压情况
- 通知发送成功率

## 虚拟线程生命周期管理

### 问题：虚拟线程被过早终止

当主线程（HTTP请求线程）结束时，如果虚拟线程还没有完成执行，它可能会被强制终止，导致异常通知发送失败。

#### 问题原因

1. **线程绑定关系**：直接使用 `Thread.ofVirtual().start()` 创建的虚拟线程与主线程绑定
2. **生命周期依赖**：当主线程结束时，相关的虚拟线程也会被终止
3. **执行中断**：这可能导致异常通知发送失败，影响问题排查

#### 解决方案

使用虚拟线程池管理虚拟线程的生命周期：

```java
// ❌ 不推荐：直接创建虚拟线程
Thread.ofVirtual().start(() -> {
    // 可能被主线程终止，导致通知发送失败
    sendNotification();
});

// ✅ 推荐：使用线程池
executorService.submit(() -> {
    // 确保能够完成执行，不会被主线程终止
    sendNotification();
});
```

#### 线程池配置

```java
@Configuration
public class ExceptionNotificationConfig {
    
    @Bean("exceptionNotificationExecutor")
    public ExecutorService exceptionNotificationExecutor() {
        // 创建虚拟线程池，确保线程能够完成执行
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        return executor;
    }
}
```

#### 优势

1. **生命周期管理**：线程池会管理虚拟线程的完整生命周期
2. **独立执行**：线程池中的虚拟线程独立于主线程，不会被过早终止
3. **资源优化**：线程池可以复用虚拟线程，提高资源利用率
4. **监控支持**：可以通过线程池监控虚拟线程的执行状态

#### 最佳实践

1. **始终使用线程池**：对于异步任务，优先使用线程池而不是直接创建线程
2. **合理配置**：根据业务需求配置合适的线程池参数
3. **监控告警**：监控线程池的使用情况，及时发现异常
4. **优雅关闭**：在应用关闭时，确保线程池能够优雅地关闭 