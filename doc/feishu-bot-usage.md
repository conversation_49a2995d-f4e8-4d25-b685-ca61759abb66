# 飞书机器人使用说明

## 功能概述

本项目集成了飞书机器人功能，支持发送各种格式的飞书消息，包括：
- 文本消息
- 富文本消息
- 卡片消息
- 通知消息
- 错误通知

## 配置说明

在 `application.yaml` 中添加以下配置：

```yaml
# 飞书机器人配置
feishu:
  bot:
    # 飞书机器人Webhook地址
    webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url
    # 飞书机器人密钥（用于签名验证）
    secret: your-secret-key
    # 是否启用飞书机器人
    enabled: true
    # 请求超时时间（毫秒）
    timeout: 10000
```

## API 接口

### 1. 测试连接
```
GET /api/feishu/test
```
测试飞书机器人连接是否正常

### 2. 发送文本消息
```
POST /api/feishu/send/text?text=消息内容
```
发送简单的文本消息

### 3. 发送通知消息
```
POST /api/feishu/send/notification?title=标题&content=内容
```
发送带标题和内容的通知消息

### 4. 发送错误通知
```
POST /api/feishu/send/error?error=错误信息&context=上下文信息
```
发送错误通知消息

### 5. 发送富文本消息
```
POST /api/feishu/send/post
Content-Type: application/json

{
  "post": {
    "zh_cn": {
      "content": [
        {
          "tag": "text",
          "text": "富文本消息标题"
        },
        {
          "tag": "text",
          "text": "\n"
        },
        {
          "tag": "text",
          "text": "富文本消息内容"
        }
      ]
    }
  }
}
```

### 5.1. 发送简单富文本消息
```
POST /api/feishu/send/post/simple?title=标题&content=内容
```
发送简单的富文本消息，系统会自动构建正确的格式

### 6. 发送卡片消息
```
POST /api/feishu/send/card
Content-Type: application/json

{
  "card": {
    "header": {
      "title": {
        "tag": "plain_text",
        "content": "卡片标题"
      }
    },
    "elements": [
      {
        "tag": "div",
        "text": {
          "content": "卡片内容",
          "tag": "lark_md"
        }
      }
    ]
  }
}
```

**卡片消息格式说明**：
- 卡片消息使用顶级的 `card` 字段，而不是 `content` 字段
- 文本元素必须使用 `text` 对象格式，包含 `content` 和 `tag` 字段
- `tag` 字段通常为 `"lark_md"` 支持 Markdown 格式

### 7. 发送自定义消息
```
POST /api/feishu/send/custom?messageType=image
Content-Type: application/json

{
  "image_key": "img_xxx"
}
```

### 8. 测试所有消息类型
```
POST /api/feishu/send/all
```
一次性测试所有格式的消息发送功能

## 代码使用示例

### 1. 注入服务
```java
@Autowired
private FeishuBotService feishuBotService;
```

### 2. 发送文本消息
```java
// 简单文本消息
boolean result = feishuBotService.sendTextMessage("Hello, 这是一条测试消息");

// 带@功能的文本消息
FeishuTextMessage textMessage = new FeishuTextMessage();
textMessage.setText("Hello, @所有人");
textMessage.setAtAll(true);
feishuBotService.sendTextMessage(textMessage);
```

### 3. 发送通知消息
```java
// 发送简单通知
feishuBotService.sendNotification("系统通知", "这是一条系统通知消息");

// 发送错误通知
feishuBotService.sendErrorNotification("数据库连接失败", "用户登录时发生错误");
```

### 4. 发送富文本消息
```java
// 构建富文本消息
FeishuPostMessage postMessage = new FeishuPostMessage();
// ... 设置消息内容
feishuBotService.sendPostMessage(postMessage);
```

### 5. 发送卡片消息
```java
// 构建卡片消息
FeishuCardMessage cardMessage = new FeishuCardMessage();
// ... 设置卡片内容
feishuBotService.sendCardMessage(cardMessage);
```

## 测试方法

### 1. API 测试
1. 启动应用后，访问 Swagger 文档：`http://localhost:8889/api/swagger-ui.html`
2. 找到 "飞书机器人" 分组
3. 先调用 `/api/feishu/test` 测试连接
4. 然后可以测试其他接口

### 2. 单元测试
运行 `FeishuBotServiceTest` 类中的测试方法：

```bash
# 运行所有测试
mvn test -Dtest=FeishuBotServiceTest

# 运行特定测试方法
mvn test -Dtest=FeishuBotServiceTest#testAllMessageTypes
```

### 3. 测试所有消息类型
- **API方式**：调用 `POST /api/feishu/send/all`
- **单元测试**：运行 `testAllMessageTypes()` 方法
- **单独测试**：可以运行各个具体的测试方法

### 4. 支持的消息类型
- **文本消息**：简单文本、@所有人、@特定用户
- **富文本消息**：带标题、内容、链接、@用户
- **卡片消息**：简单卡片、复杂卡片、带按钮卡片
- **通知消息**：系统通知、错误通知
- **自定义消息**：图片、音频、视频、文件、贴纸、分享等

## 注意事项

1. 确保飞书机器人已正确配置 Webhook 地址
2. 如果配置了密钥，系统会自动进行签名验证
3. 可以通过 `enabled` 配置项控制是否启用飞书机器人功能
4. 所有发送失败的情况都会记录错误日志

## 富文本消息格式说明

飞书机器人支持 `post` 类型的富文本消息，格式如下：

### 正确的富文本消息格式：
```json
{
  "msg_type": "post",
  "content": {
    "post": {
      "zh_cn": {
        "title": "项目更新通知",
        "content": [
          [{
            "tag": "text",
            "text": "项目有更新: "
          }, {
            "tag": "a",
            "text": "请查看",
            "href": "http://www.example.com/"
          }, {
            "tag": "at",
            "user_id": "ou_18eac8********17ad4f02e8bbbb"
          }]
        ]
      }
    }
  }
}
```

**重要说明**：`content` 字段必须包含一个 `post` 对象，而不是直接包含 `zh_cn` 对象。

### 格式说明：
1. **消息类型**：`msg_type` 为 `"post"`
2. **标题**：`title` 字段设置消息标题
3. **内容结构**：`content` 是二维数组，每行是一个数组
4. **内容元素**：每行可以包含多个不同类型的元素

### 支持的内容类型：
- `text`: 文本内容
- `a`: 链接（需要 `text` 和 `href` 属性）
- `at`: @用户（需要 `user_id` 属性）
- `image`: 图片（需要 `image_key` 属性）

### 内容布局规则：
- **同一行元素**：文本、链接、@用户等可以在同一行显示
- **换行**：每个数组代表一行内容
- **标题**：独立显示在消息顶部

### 常见错误：
- 错误码 19002: 表示消息格式不正确，通常是 `content` 结构有问题
- 确保每个 `ContentItem` 都有正确的 `tag` 和对应的属性
- 确保 `content` 是二维数组格式 