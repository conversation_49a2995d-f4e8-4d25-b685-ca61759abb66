# 飞书机器人使用说明

## 功能概述

本项目集成了飞书机器人功能，支持发送各种格式的飞书消息，包括：
- 文本消息
- 富文本消息
- 卡片消息
- 通知消息
- 自定义消息

## 配置说明

在 `application-local.yaml` 中添加以下配置：

```yaml
# 飞书机器人配置
feishu:
  bot:
    # 飞书机器人Webhook地址
    webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url
    # 飞书机器人密钥（用于签名验证，可选）
    secret: your-secret-key
    # 是否启用飞书机器人
    enabled: true
    # 请求超时时间（毫秒）
    timeout: 10000
```

## 代码使用示例

### 1. 注入服务
```java
@Autowired
private FeishuBotService feishuBotService;
```

### 2. 发送文本消息
```java
// 简单文本消息
boolean result = feishuBotService.sendTextMessage("Hello, 这是一条测试消息");

// 带@功能的文本消息
FeishuTextMessage textMessage = new FeishuTextMessage();
textMessage.setText("Hello, @所有人");
textMessage.setAtAll(true);
// 或者@特定用户
textMessage.setAtUserIds(new String[]{"user123", "user456"});
feishuBotService.sendTextMessage(textMessage);
```

### 3. 发送通知消息
```java
// 发送简单通知
feishuBotService.sendNotification("系统通知", "这是一条系统通知消息");

// 发送错误通知
feishuBotService.sendErrorNotification("数据库连接失败", "用户登录时发生错误");
```

### 4. 发送富文本消息
```java
// 构建富文本消息
FeishuPostMessage postMessage = new FeishuPostMessage();
FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();

// 设置标题
detail.setTitle("项目更新通知");

// 构建内容（二维数组格式）
List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();

// 第一行：文本和链接
List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
FeishuPostMessage.ContentItem textItem = new FeishuPostMessage.ContentItem();
textItem.setTag("text");
textItem.setText("项目有更新: ");
firstRow.add(textItem);

FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
linkItem.setTag("a");
linkItem.setText("请查看");
linkItem.setHref("http://www.example.com/");
firstRow.add(linkItem);
contentList.add(firstRow);

detail.setContent(contentList);
postContent.setZh_cn(detail);
postMessage.setPost(postContent);

feishuBotService.sendPostMessage(postMessage);
```

### 5. 发送卡片消息
```java
// 构建卡片消息
FeishuCardMessage cardMessage = new FeishuCardMessage();

// 设置卡片头部
FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
cardTitle.setContent("消息标题");
cardTitle.setTag("plain_text");
header.setTitle(cardTitle);
cardMessage.setHeader(header);

// 设置卡片主体
FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
List<FeishuCardMessage.CardElement> elements = new ArrayList<>();

// 添加内容元素
FeishuCardMessage.CardElement contentElement = new FeishuCardMessage.CardElement();
contentElement.setTag("markdown");
contentElement.setContent("这是消息内容");
contentElement.setTextAlign("left");
contentElement.setMargin("0px 0px 0px 0px");
elements.add(contentElement);

body.setElements(elements);
cardMessage.setBody(body);

feishuBotService.sendCardMessage(cardMessage);
```

### 6. 发送自定义消息
```java
// 发送图片消息
Map<String, String> imageContent = new HashMap<>();
imageContent.put("image_key", "img_xxx");
feishuBotService.sendCustomMessage("image", imageContent);

// 发送分享消息
Map<String, String> shareContent = new HashMap<>();
shareContent.put("share_chat_id", "oc_xxx");
feishuBotService.sendCustomMessage("share_chat", shareContent);
```

## 测试方法

### 1. 单元测试
运行 `FeishuBotServiceTest` 类中的测试方法：

```bash
# 运行所有测试
mvn test -Dtest=FeishuBotServiceTest

# 运行特定测试方法
mvn test -Dtest=FeishuBotServiceTest#testAllMessageTypes
```

### 2. 测试所有消息类型
运行 `testAllMessageTypes()` 方法可以一次性测试所有格式的消息发送功能。

### 3. 支持的消息类型
- **文本消息**：简单文本、@所有人、@特定用户
- **富文本消息**：带标题、内容、链接、@用户
- **卡片消息**：简单卡片、复杂卡片、带按钮卡片
- **通知消息**：系统通知、错误通知
- **自定义消息**：图片、音频、视频、文件、贴纸、分享等

## 富文本消息格式说明

飞书机器人支持 `post` 类型的富文本消息，格式如下：

### 正确的富文本消息格式：
```json
{
  "msg_type": "post",
  "content": {
    "post": {
      "zh_cn": {
        "title": "项目更新通知",
        "content": [
          [{
            "tag": "text",
            "text": "项目有更新: "
          }, {
            "tag": "a",
            "text": "请查看",
            "href": "http://www.example.com/"
          }, {
            "tag": "at",
            "user_id": "ou_18eac8********17ad4f02e8bbbb"
          }]
        ]
      }
    }
  }
}
```

**重要说明**：`content` 字段必须包含一个 `post` 对象，而不是直接包含 `zh_cn` 对象。

### 格式说明：
1. **消息类型**：`msg_type` 为 `"post"`
2. **标题**：`title` 字段设置消息标题
3. **内容结构**：`content` 是二维数组，每行是一个数组
4. **内容元素**：每行可以包含多个不同类型的元素

### 支持的内容类型：
- `text`: 文本内容
- `a`: 链接（需要 `text` 和 `href` 属性）
- `at`: @用户（需要 `user_id` 属性）
- `image`: 图片（需要 `image_key` 属性）

### 内容布局规则：
- **同一行元素**：文本、链接、@用户等可以在同一行显示
- **换行**：每个数组代表一行内容
- **标题**：独立显示在消息顶部

## 注意事项

1. **配置要求**：确保飞书机器人已正确配置 Webhook 地址
2. **签名验证**：如果配置了密钥，系统会自动进行签名验证
3. **功能开关**：可以通过 `enabled` 配置项控制是否启用飞书机器人功能
4. **异常处理**：所有发送失败的情况都会记录错误日志，不会影响主业务流程
5. **网络依赖**：消息发送依赖网络连接，网络异常时发送可能失败

## 常见问题

### 1. 消息发送失败
- 检查Webhook地址是否正确
- 检查网络连接是否正常
- 查看应用日志中的详细错误信息

### 2. 签名验证失败
- 检查密钥配置是否正确
- 确认密钥与飞书机器人设置中的密钥一致

### 3. 富文本消息格式错误
- 确保 `content` 字段包含 `post` 对象
- 检查 `content` 是否为二维数组格式
- 验证每个 `ContentItem` 都有正确的 `tag` 和对应属性 