package com.unipus.digitalbook.aop.bussinese;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.params.Params;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Controller层参数验证拦截器
 * 拦截所有Controller中的方法，对实现了Params接口的参数进行验证
 */
@Aspect
@Component
@Order(1)  // 优先级较高，确保在其他拦截器之前执行
@Slf4j
public class ParamValidInterceptor {

    /**
     * 拦截所有带有@RestController注解的类中的所有方法
     */
    @Around(
            "@within(org.springframework.web.bind.annotation.RestController) || " +
                    "@within(org.springframework.stereotype.Controller)"
    )
    public Object validateParameters(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法的所有参数
        Object[] args = joinPoint.getArgs();

        // 遍历所有参数
        for (Object arg : args) {
            // 如果参数为null，跳过验证
            if (arg == null) {
                continue;
            }

            // 检查参数是否实现了Params接口
            if (arg instanceof Params params) {
                try {
                    // 调用参数的valid方法进行验证
                    params.valid();
                } catch (Exception e) {
                    // 可以根据需要自定义异常处理
                    // 获取被调用的方法名和类名用于日志
                    String methodName = joinPoint.getSignature().getName();
                    String className = joinPoint.getTarget().getClass().getName();
                    log.info("""
                                    [参数验证失败]
                                    |-> 类名: {}
                                    |-> 方法名: {}
                                    |-> 参数值: {}
                                    |-> 错误信息: {}
                                    """,
                            className,
                            methodName,
                            params,
                            e.getMessage()
                    );
                    return Response.fail(Response.StatusCode.BAD_REQUEST, e.getMessage());
                }
            }
        }

        // 验证通过后继续调用原方法
        return joinPoint.proceed();
    }
}
