package com.unipus.digitalbook.aop.locker;

import com.unipus.digitalbook.common.exception.permission.PermissionCheckException;
import com.unipus.digitalbook.common.utils.UserUtil;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.TypedValue;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 数据更新锁
 * <AUTHOR>
 */
@Aspect
@Component
public class DataLockerAspect {
    private static final Logger log = LoggerFactory.getLogger(DataLockerAspect.class);

    @Resource
    private RedissonClient redissonClient;

    // 获取方法上的参数名
    private static final ParameterNameDiscoverer PARAMETER_NAME_DISCOVERER = new DefaultParameterNameDiscoverer();
    // 解析器
    private final ExpressionParser parser = new SpelExpressionParser();

    @Pointcut("@annotation(com.unipus.digitalbook.aop.locker.DataLocker)")
    public void dataLockerPointcut() {}

    @Around("dataLockerPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法上的DataLocker注解
        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        DataLocker dataLocker = signature.getMethod().getAnnotation(DataLocker.class);

        // 解析出注解里的动态参数
        String key = getRedisKeyFromDynamicParam(joinPoint, dataLocker);
        if (!StringUtils.hasText(key)) {
            // 执行原方法
            return joinPoint.proceed();
        }

        // 获取分布式锁
        String redisKey = dataLocker.prefix() + ":" + key;
        RLock lock = redissonClient.getLock(redisKey);
        if (lock==null) {
            log.error("获取数据更新分布锁失败");
            throw new PermissionCheckException("获取数据更新分布式锁失败");
        }
        // 尝试加锁，最多等待timeout秒，上锁后keepAliveTime秒自动解锁
        boolean isLocked = lock.tryLock( dataLocker.waitTime(), dataLocker.leaseTime(), TimeUnit.SECONDS);
        if (!isLocked) {
            log.error("数据更新分布锁加锁失败");
            throw new PermissionCheckException("获取数据更新分布式锁失败");
        }
        try {
            // 执行原方法
            return joinPoint.proceed();
        } finally {
            // 释放锁
            lock.unlock();
        }
    }

    /**
     * 获取注解里的动态参数拼接RedisKey
     * @param joinPoint : 连接点
     * @param dataLocker : 注解
     * @return : RedisKey
     */
    private String getRedisKeyFromDynamicParam(ProceedingJoinPoint joinPoint, DataLocker dataLocker) {
        StringBuilder sb = new StringBuilder();

        // 当前用户并发控制（一个用户一个并发）
        if(dataLocker.single()){
            sb.append(UserUtil.getCurrentUser()).append(":");
        }

        // 动态参数为空，直接返回
        if(!StringUtils.hasText(dataLocker.value())){
            return sb.toString();
        }

        // 表达式对象
        Expression expression = parser.parseExpression(dataLocker.value());
        // 上下文对象
        EvaluationContext context = new MethodBasedEvaluationContext(TypedValue.NULL, resolveMethod(joinPoint),
                joinPoint.getArgs(), PARAMETER_NAME_DISCOVERER);
        // 解析出的动态参数
        Object value = expression.getValue(context);
        sb.append(":").append(ObjectUtils.nullSafeToString(value));
        return sb.toString();
    }

    /**
     * 获取注解所在的method
     * @param joinPoint : 连接点
     * @return : 方法对象
     */
    private Method resolveMethod(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class<?> targetClass = joinPoint.getTarget().getClass();

        return getDeclaredMethodFor(targetClass, signature.getName(),
                signature.getMethod().getParameterTypes());
    }

    /**
     * 获取方法对象
     * @param clazz : 类对象
     * @param name : 方法名
     * @param parameterTypes : 参数类型
     * @return : 方法对象
     */
    private Method getDeclaredMethodFor(Class<?> clazz, String name, Class<?>... parameterTypes) {
        try {
            return clazz.getDeclaredMethod(name, parameterTypes);
        } catch (NoSuchMethodException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getDeclaredMethodFor(superClass, name, parameterTypes);
            }
        }
        throw new IllegalStateException("Cannot resolve target method: " + name);
    }

}