package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.paper.*;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.enums.ScoreTypeEnum;
import com.unipus.digitalbook.model.enums.TagTypeEnum;
import com.unipus.digitalbook.model.params.paper.*;
import com.unipus.digitalbook.model.params.paper.question.PaperQuestionListParam;
import com.unipus.digitalbook.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 试卷管理控制器
 *
 * 提供试卷相关的REST API接口，包括试卷的创建、查询、管理等功能。
 * 支持三种类型的试卷：常规卷、挑战卷、诊断卷。
 *
 * 主要功能模块：
 * - 试卷基本信息管理（创建、保存、查询、删除）
 * - 试卷题目管理（保存题目列表、查询题目）
 * - 试卷版本管理（版本查询、版本发布）
 * - 试卷引用管理（教材章节引用）
 * - 试卷标签管理（知识点标签）
 * - 题库管理（挑战卷题库配置）
 *
 * 接口权限：需要用户登录认证
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/paper")
@Tag(name = "试卷相关功能", description = "试卷相关功能接口")
public class PaperController extends BaseController {

    @Resource
    private PaperService paperService;
    @Resource
    private QuestionBankService questionBankService;
    @Resource
    private PaperInstanceService paperInstanceService;
    @Resource
    private PaperReferenceService paperReferenceService;
    @Resource
    private PaperAnswerService paperAnswerService;
    @Resource
    private TagService tagService;

    @GetMapping("/getPaperTopTagList")
    @Operation(summary = "取得试卷标签列表", description = "取得试卷标签列表")
    public Response<PaperTagListDTO> getPaperTopTagList() {
        return Response.success(new PaperTagListDTO(tagService.getTopLevelTagListByType(TagTypeEnum.SKILL_POINT)));
    }

    @PostMapping("/savePaper")
    @Operation(summary = "保存试卷", description = "保存试卷")
    public Response<IdDTO<String>> savePaper(@RequestBody PaperParam param) {
        // 保存试卷基本信息
        String paperId = paperService.savePaper(param.toEntity(), getCurrentUserId());
        if(Boolean.TRUE.equals(param.getProcessQuestions())){
            // 保存题目列表
            param.getQuestionListParam().setParentId(paperId);
            paperService.saveQuestions(param.getQuestionListParam(), getCurrentUserId());
        }
        return Response.success(new IdDTO<>(paperId));
    }

    @PostMapping("/saveQuestions")
    @Operation(summary = "保存题目列表", description = "保存题目列表")
    public Response<IdDTO<String>> saveQuestions(@RequestBody PaperQuestionListParam param) {
        return Response.success(new IdDTO<>(paperService.saveQuestions(param, getCurrentUserId())));
    }

    @PostMapping("/getPaperList")
    @Operation(summary = "查询试卷列表", description = "查询试卷列表")
    public Response<PaperListDTO> getPaperList(@RequestBody PaperQueryParam param) {
        return Response.success(new PaperListDTO(paperService.getDefaultVersionPaperList(param)));
    }

    @GetMapping("/getPaperDetail")
    @Operation(summary = "取得试卷详情", description = "取得试卷详情")
    public Response<PaperDTO> getPaperDetail(@RequestParam("paperId") String paperId) {
        // 取得试卷基本信息
        Paper paperDetail = paperService.getPaperDetail(paperId, null);
        // 取得题目列表
        PaperQuestionListDTO paperQuestionListDTO = new PaperQuestionListDTO(paperService.getQuestions(paperId, null), true);
        return Response.success(new PaperDTO(paperDetail, paperQuestionListDTO));
    }

    @GetMapping("/getQuestions")
    @Operation(summary = "查询题目列表", description = "查询题目列表")
    public Response<PaperQuestionListDTO> getQuestions(@RequestParam("parentBizGroupId") String parentBizGroupId) {
        return Response.success(new PaperQuestionListDTO(paperService.getQuestions(parentBizGroupId, null), true));
    }

    @PostMapping("/deletePaper")
    @Operation(summary = "删除试卷", description = "删除试卷")
    public Response<BooleanDTO> deletePaper(@RequestBody IdDTO<String> param) {
        return Response.success(new BooleanDTO(paperService.deletePaper(param.getId(), getCurrentUserId())));
    }

    @PostMapping("/saveQuestionBank")
    @Operation(summary = "保存题库", description = "保存题库")
    public Response<IdDTO<String>> saveQuestionBank(@RequestBody QuestionBankParam param) {
        return Response.success(new IdDTO<>(questionBankService.saveQuestionBank(param.toEntity(), getCurrentUserId())));
    }

    @PostMapping("/getQuestionBankList")
    @Operation(summary = "查询题库列表", description = "查询题库列表")
    public Response<QuestionBankListDTO> getQuestionBankList(@RequestBody BankQueryParam param) {
        return Response.success(new QuestionBankListDTO(questionBankService.getQuestionBankList(param.getPaperId())));
    }

    @PostMapping("/getChallengePaperStatInfo")
    @Operation(summary = "查询挑战卷统计信息", description = "查询挑战卷统计信息")
    public Response<QuestionBankStatDTO> getChallengePaperStatInfo(@RequestBody BankQueryParam param) {
        return Response.success(QuestionBankStatDTO.build(
                questionBankService.getQuestionBankStatInfo(param.getPaperId(), param.getVersionNumber())));
    }

    @GetMapping("/getBankDetail")
    @Operation(summary = "取得题库详情", description = "取得题库详情")
    public Response<QuestionBankDTO> getBankDetail(@RequestParam("bankId") String bankId) {
        // 取得试卷基本信息
        QuestionBank questionBank = questionBankService.getQuestionBankDetail(bankId, null);
        // 取得题目列表
        PaperQuestionListDTO paperQuestionListDTO = new PaperQuestionListDTO(paperService.getQuestions(bankId, null), false);
        return Response.success(new QuestionBankDTO(questionBank, paperQuestionListDTO));
    }

    @PostMapping("/deleteQuestionBank")
    @Operation(summary = "删除题库", description = "删除题库")
    public Response<BooleanDTO> deleteQuestionBank(@RequestBody IdDTO<String> param) {
        return Response.success(new BooleanDTO(questionBankService.deleteQuestionBank(param.getId(), getCurrentUserId())));
    }

    @PostMapping("/insertPaperReference")
    @Operation(summary = "在教材中添加试卷引用", description = "在教材中添加试卷引用")
    public Response<BooleanDTO> insertPaperReference(@RequestBody PaperReferenceParam param) {
        return Response.success(new BooleanDTO(paperReferenceService.insertPaperReference(param.toEntity(), getCurrentUserId())));
    }

    @PostMapping("/deletePaperReference")
    @Operation(summary = "删除教材中的试卷引用", description = "删除教材中的试卷引用")
    public Response<IdDTO<Boolean>> deletePaperReference(@RequestBody PaperReferenceParam param) {
        return Response.success(new IdDTO<>(paperReferenceService.deletePaperReference(param.toEntity(), getCurrentUserId())));
    }

    @GetMapping("/getPaperReferenceList")
    @Operation(summary = "查询教材中试卷的引用列表", description = "查询教材中试卷的引用列表(输出最新发布版本号)")
    public Response<PaperReferenceListDTO> getPaperReferenceList(@RequestParam("bookId") String bookId) {
        return Response.success(new PaperReferenceListDTO(paperReferenceService.getPaperReferenceList(bookId)));
    }

    @PostMapping("/instance/generatePreviewPaperInstance")
    @Operation(summary = "生成预览试卷实例(编辑态用户可见)", description = "生成预览试卷实例（返回答案）")
    public Response<PaperInstanceDTO> generatePreviewPaperInstance(@RequestBody PaperInstanceEditPreviewParam param) {
        return Response.success(new PaperInstanceDTO(paperInstanceService.generatePreviewPaperInstance(
                param.toEntity(getUserAccessInfo())), true));
    }

    @PostMapping("/instance/getLatestPaperInstance")
    @Operation(summary = "获取用户特定试卷的最近一次试卷实例信息", description = "获取用户特定试卷的最近一次试卷实例信息")
    public Response<PaperInstanceDTO> getLatestPaperInstance(@RequestBody PaperInstanceParam param) {
        PaperInstance entity = param.toEntity(getUserAccessInfo());
        PaperInstance paperInstance = paperInstanceService.getLatestPaperInstance(entity, null);
        return Response.success(PaperInstanceDTO.build(paperInstance, false));
    }

    @GetMapping("/instance/getPaperAnalysis")
    @Operation(summary = "取得试卷作答解析", description = "取得试卷作答解析")
    public Response<PaperAnalysisDTO> getPaperAnalysis(@RequestParam("instanceId") String instanceId) {
        // 取得试卷解析
        PaperInstance paperInstance = paperInstanceService.getPaperAnalysis(instanceId);
        // 取得用户作答
        QueryPaperAnswer param = new QueryPaperAnswer(instanceId, paperInstance.getPaperType(), getUserAccessInfo());
        UserPaperAnswer userPaperAnswer = paperAnswerService.getUserPaperAnswer(param);
        return Response.success(new PaperAnalysisDTO(paperInstance, userPaperAnswer));
    }

    @PostMapping("/answer/getPreviewPaperAnswer")
    @Operation(summary = "取得预览作答记录", description = "取得预览作答记录")
    public Response<UserPaperAnswerDTO> getPreviewPaperAnswer(@RequestBody UserPaperAnswerParam param) {
        return Response.success(UserPaperAnswerDTO.build(paperAnswerService.getPreviewPaperAnswer(
                param, getUserAccessInfo())));
    }

    @PostMapping("/answer/saveUserPaperAnswer")
    @Operation(summary = "保存用户作答记录", description = "保存用户作答记录")
    public Response<UserPaperScoreDTO> saveUserPaperAnswer(@RequestBody UserPaperAnswerParam param) {
        return Response.success(new UserPaperScoreDTO(paperAnswerService.saveUserPaperAnswer(param, getUserAccessInfo())));
    }

    @PostMapping("/answer/getUserPaperAnswer")
    @Operation(summary = "取得用作答记录", description = "取得用作答记录")
    public Response<UserPaperAnswerDTO> getUserPaperAnswer(@RequestBody QueryPaperAnswerParam param) {
        return Response.success(UserPaperAnswerDTO.build(paperAnswerService.getUserPaperAnswer(param.toEntity(getUserAccessInfo()))));
    }

    @PostMapping("/answer/submitUserPaperAnswer")
    @Operation(summary = "提交试卷作答记录", description = "提交试卷作答记录")
    public Response<UserPaperScoreDTO> submitUserPaperAnswer(@RequestBody UserPaperAnswerParam param) {
        return Response.success(new UserPaperScoreDTO(paperAnswerService.submitUserPaperAnswer(param, getUserAccessInfo())));
    }

    @PostMapping("/answer/submitUserChallengeScore")
    @Operation(summary = "提交用户挑战卷最佳成绩", description = "提交用户挑战卷最佳成绩")
    public Response<UserPaperScoreDTO> submitUserChallengeScore(@RequestBody UserPaperInfoParam param) {
        return Response.success(new UserPaperScoreDTO(
                paperAnswerService.submitUserChallengeScore(param.toEntity(getUserAccessInfo()), getUserAccessInfo())));
    }

    @PostMapping("/answer/getUserPaperScore")
    @Operation(summary = "取得用户试卷成绩(评价)", description = "取得用户试卷成绩(评价)")
    public Response<UserPaperScoreDTO> getUserPaperScore(@RequestBody UserPaperInfoParam param) {
        return Response.success(UserPaperScoreDTO.build(paperAnswerService
                .getUserPaperScore(param.toEntity(getUserAccessInfo()), getUserAccessInfo()), ScoreTypeEnum.ORIGINAL));
    }

    @PostMapping("/answer/getUserPaperSubmitHistory")
    @Operation(summary = "取得用户试卷成绩提交记录", description = "取得用户试卷成绩提交记录")
    public Response<PaperScoreRecordListDTO> getUserPaperSubmitHistory(@RequestBody UserPaperInfoParam param) {
        return Response.success(PaperScoreRecordListDTO.build(
                paperAnswerService.getUserPaperSubmitHistory(param.toEntity(getUserAccessInfo())), ScoreTypeEnum.ORIGINAL));
    }

}
