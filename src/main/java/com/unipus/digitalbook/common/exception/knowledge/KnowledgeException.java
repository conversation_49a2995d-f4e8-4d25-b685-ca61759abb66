package com.unipus.digitalbook.common.exception.knowledge;

/**
 * <AUTHOR>
 * @date 2025/3/10 10:03
 */
public class KnowledgeException extends RuntimeException {

    private Integer code;
    private Object data;

    public KnowledgeException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public KnowledgeException(Integer code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public KnowledgeException(String message, Throwable cause) {
        super(message, cause);
    }

    public Integer getCode() {
        return code;
    }
}
