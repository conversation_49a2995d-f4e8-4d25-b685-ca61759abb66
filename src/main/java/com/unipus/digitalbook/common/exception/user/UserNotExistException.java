package com.unipus.digitalbook.common.exception.user;

public class UserNotExistException extends RuntimeException{
    public UserNotExistException(String message) {
        super(message);
    }
    public UserNotExistException() {
        super("用户信息不存在。");
    }
    public UserNotExistException(String message, Throwable cause) {
        super(message, cause);
    }
    public UserNotExistException(Throwable cause) {
        super(cause);
    }
    protected UserNotExistException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
    public static UserNotExistException newInstance(String message) {
        return new UserNotExistException(message);
    }


}