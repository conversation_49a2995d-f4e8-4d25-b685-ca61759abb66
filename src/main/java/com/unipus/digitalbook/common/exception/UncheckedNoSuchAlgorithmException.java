package com.unipus.digitalbook.common.exception;

import java.security.NoSuchAlgorithmException;
public class UncheckedNoSuchAlgorithmException extends RuntimeException {

    /**
     * 创建一个新的未检查的 {@code NoSuchAlgorithmException} 异常实例。
     * 这个新的异常会包装一个已有的 {@code NoSuchAlgorithmException} 异常。
     *
     * @param cause 导致此异常的原始 {@code NoSuchAlgorithmException} 异常
     */
    public UncheckedNoSuchAlgorithmException(final NoSuchAlgorithmException cause) {
        super(cause);
    }

    /**
     * 获取导致此异常的原始 {@link NoSuchAlgorithmException} 异常。
     *
     * @return 导致此异常的原始 {@link NoSuchAlgorithmException} 异常
     */
    @Override
    public NoSuchAlgorithmException getCause() {
        return (NoSuchAlgorithmException) super.getCause();
    }
}


