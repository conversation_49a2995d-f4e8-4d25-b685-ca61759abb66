package com.unipus.digitalbook.common.exception.user;

/**
 * 用户已激活异常，用于表示用户状态为已激活时，无法执行某些操作。
 */
public class UserAlreadyActivatedException extends UserStateException {

    /**
     * 构造一个新的用户已激活异常。
     */
    public UserAlreadyActivatedException() {
        super("用户已激活，无法修改。");
    }

    /**
     * 构造一个新的用户已激活异常。
     *
     * @param message 自定义异常信息
     */
    public UserAlreadyActivatedException(String message) {
        super(message);
    }
}