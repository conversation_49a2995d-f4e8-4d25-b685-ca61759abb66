package com.unipus.digitalbook.common.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Optional;

@Component
public class CookieUtil {


    public void addRefreshTokenCookie(HttpServletResponse response, String refreshToken) {
        // 创建一个新的ResponseCookie对象，用于设置刷新令牌的Cookie
        ResponseCookie cookie = ResponseCookie.from("refresh_token", refreshToken)
                .httpOnly(true)                // 设置HttpOnly标志，防止客户端脚本访问Cookie
                .secure(true)                  // 确保Cookie仅通过HTTPS传输，增加安全性
                .sameSite("Strict")            // 设置SameSite属性为Strict，防止跨站请求伪造（CSRF）攻击
                .path("/api/auth/refresh")     // 限制Cookie的路径，确保Cookie只能在刷新令牌的API接口中使用
                .maxAge(Duration.ofDays(30))   // 设置Cookie的有效期为30天
                .domain("ipublish.unipus.cn")     // 设置Cookie的域名，确保在指定域名下有效
                .build();

        // 将构建好的Cookie添加到HTTP响应头中
        response.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }


    public void deleteRefreshTokenCookie(HttpServletResponse response) {
        // 创建一个新的ResponseCookie对象，用于删除refresh_token cookie
        ResponseCookie cookie = ResponseCookie.from("refresh_token", "")
                .httpOnly(true)                 // 仅允许服务器端访问cookie
                .secure(true)                   // 仅在HTTPS连接中发送cookie
                .sameSite("Strict")             // 限制cookie的跨站发送
                .path("/api/auth/refresh")      // 设置cookie的路径
                .maxAge(0)                      // 设置cookie的最大存活时间为0，即立即过期
                .domain("ipublish.unipus.cn")      // 设置cookie的域名
                .build();

        // 将构建的cookie添加到HTTP响应头中
        response.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }


    public Optional<String> getRefreshTokenFromCookies(HttpServletRequest request) {
        // 获取请求中的所有Cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            // 遍历所有Cookie
            for (Cookie cookie : cookies) {
                // 检查Cookie的名称是否为"refresh_token"
                if ("refresh_token".equals(cookie.getName())) {
                    // 如果找到匹配的Cookie，返回其值
                    return Optional.of(cookie.getValue());
                }
            }
        }
        // 如果没有找到匹配的Cookie，返回一个空的Optional对象
        return Optional.empty();
    }

}
