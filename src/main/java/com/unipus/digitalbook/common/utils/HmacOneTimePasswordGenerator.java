package com.unipus.digitalbook.common.utils;


import com.unipus.digitalbook.common.exception.UncheckedNoSuchAlgorithmException;
import com.unipus.digitalbook.common.exception.business.BizException;

import javax.crypto.Mac;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.Locale;

/**
 * Generates HMAC-based one-time passwords (HOTP) as specified in
 * <a href="https://tools.ietf.org/html/rfc4226">RFC&nbsp;4226</a>. {@code HmacOneTimePasswordGenerator} instances are
 * thread-safe and may be shared between threads.
 */
public class HmacOneTimePasswordGenerator {
    private final Mac prototypeMac;
    private final int passwordLength;

    private final int modDivisor;

    private final String formatString;

    /**
     * 默认的一次性密码长度（十进制位数）。
     */
    public static final int DEFAULT_PASSWORD_LENGTH = 6;

    /**
     * HOTP标准指定的HMAC算法。
     */
    static final String HOTP_HMAC_ALGORITHM = "HmacSHA1";

    /**
     * 使用默认的一次性密码长度（{@value #DEFAULT_PASSWORD_LENGTH}位）创建一个新的基于HMAC的一次性密码（HOTP）生成器。
     */
    public HmacOneTimePasswordGenerator() {
        this(DEFAULT_PASSWORD_LENGTH);
    }

    /**
     * 使用给定的一次性密码长度创建一个新的基于HMAC的一次性密码（HOTP）生成器。
     *
     * @param passwordLength 要生成的一次性密码的长度（十进制位数）；必须在6到8之间（包括6和8）
     */
    public HmacOneTimePasswordGenerator(final int passwordLength) {
        this(passwordLength, HOTP_HMAC_ALGORITHM);
    }

    /**
     * <p>使用给定的一次性密码长度和算法创建一个新的基于HMAC的一次性密码生成器。注意，
     * <a href="https://tools.ietf.org/html/rfc4226">RFC&nbsp;4226</a> 指定HOTP必须始终使用HMAC-SHA1作为算法，
     * 但派生的一次性密码系统（如TOTP）可能允许其他算法。</p>
     *
     * @param passwordLength 要生成的一次性密码的长度（十进制位数）；必须在6到8之间（包括6和8）
     * @param algorithm      在生成密码时使用的{@link javax.crypto.Mac}算法名称；注意HOTP只允许
     *                       {@value #HOTP_HMAC_ALGORITHM}，但派生标准（如TOTP）可能允许其他算法
     * @throws UncheckedNoSuchAlgorithmException 如果给定的算法不受底层JRE支持
     */
    HmacOneTimePasswordGenerator(final int passwordLength, final String algorithm) throws UncheckedNoSuchAlgorithmException {
        try {
            this.prototypeMac = Mac.getInstance(algorithm);
        } catch (final NoSuchAlgorithmException e) {
            throw new UncheckedNoSuchAlgorithmException(e);
        }

        switch (passwordLength) {
            case 6: {
                this.modDivisor = 1_000_000;
                this.formatString = "%06d";
                break;
            }

            case 7: {
                this.modDivisor = 10_000_000;
                this.formatString = "%07d";
                break;
            }

            case 8: {
                this.modDivisor = 100_000_000;
                this.formatString = "%08d";
                break;
            }

            default: {
                throw new IllegalArgumentException("一次性密码长度必须在6到8位之间。");
            }
        }

        this.passwordLength = passwordLength;
    }

    /**
     * 使用给定的密钥和计数器值生成一次性密码。
     *
     * @param key     用于生成密码的密钥
     * @param counter 要为其生成密码的计数器值
     * @return 一次性密码的整数表示形式；调用者需要自行格式化密码以显示
     * @throws InvalidKeyException 如果给定的密钥不适用于初始化此生成器的{@link Mac}
     */
    public int generateOneTimePassword(final Key key, final long counter) throws InvalidKeyException {
        final Mac mac = getMac();
        final ByteBuffer buffer = ByteBuffer.allocate(mac.getMacLength());

        buffer.putLong(0, counter);

        try {
            final byte[] array = buffer.array();

            mac.init(key);
            mac.update(array, 0, 8);
            mac.doFinal(array, 0);
        } catch (final ShortBufferException e) {
            // 我们在构造时分配了一个至少匹配MAC长度的缓冲区，所以这不应该发生。
            throw new BizException(e.getMessage());
        }

        final int offset = buffer.get(buffer.capacity() - 1) & 0x0f;
        return (buffer.getInt(offset) & 0x7fffffff) % this.modDivisor;
    }

    private Mac getMac() {
        try {
            // 克隆通常比Mac.getInstance更便宜，但并不保证总是被支持。
            return (Mac) this.prototypeMac.clone();
        } catch (CloneNotSupportedException e) {
            try {
                return Mac.getInstance(this.prototypeMac.getAlgorithm());
            } catch (final NoSuchAlgorithmException ex) {
                // 这应该是不可能的；我们正在从已经存在的Mac获取算法，因此该算法必须受支持。
                throw new BizException(ex.getMessage());
            }
        }
    }

    /**
     * 使用给定的密钥和计数器值生成一次性密码，并使用系统默认区域设置将其格式化为字符串。
     *
     * @param key     用于生成密码的密钥
     * @param counter 要为其生成密码的计数器值
     * @return 一次性密码的字符串表示形式
     * @throws InvalidKeyException 如果给定的密钥不适用于初始化此生成器的{@link Mac}
     * @see Locale#getDefault()
     */
    public String generateOneTimePasswordString(final Key key, final long counter) throws InvalidKeyException {
        return this.generateOneTimePasswordString(key, counter, Locale.getDefault());
    }

    /**
     * 使用给定的密钥和计数器值生成一次性密码，并使用给定区域设置将其格式化为字符串。
     *
     * @param key     用于生成密码的密钥
     * @param counter 要为其生成密码的计数器值
     * @param locale  格式化期间应用的区域设置
     * @return 一次性密码的字符串表示形式
     * @throws InvalidKeyException 如果给定的密钥不适用于初始化此生成器的{@link Mac}
     */
    public String generateOneTimePasswordString(final Key key, final long counter, final Locale locale) throws InvalidKeyException {
        return this.formatOneTimePassword(generateOneTimePassword(key, counter), locale);
    }

    /**
     * 使用给定区域设置将一次性密码格式化为固定长度的字符串。
     *
     * @param oneTimePassword 要格式化为字符串的一次性密码
     * @param locale          格式化期间应用的区域设置
     * @return 给定一次性密码的字符串表示形式
     */
    String formatOneTimePassword(final int oneTimePassword, final Locale locale) {
        return String.format(locale, formatString, oneTimePassword);
    }

    /**
     * 返回此生成器生成的一次性密码的长度（十进制位数）。
     *
     * @return 此生成器生成的一次性密码的长度（十进制位数）
     */
    public int getPasswordLength() {
        return this.passwordLength;
    }

    /**
     * 返回此生成器使用的HMAC算法名称。
     *
     * @return 此生成器使用的HMAC算法名称
     */
    public String getAlgorithm() {
        return this.prototypeMac.getAlgorithm();
    }


    public static void main(String[] args) throws InvalidKeyException {
        HmacOneTimePasswordGenerator generator = new HmacOneTimePasswordGenerator(6);
        for (int i = 0; i < 10; i++) {
            System.out.println(generator.generateOneTimePasswordString(new SecretKeySpec("11111".getBytes(), "HmacSHA1"), i));
        }
    }
}

