package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.conf.security.JwtProperties;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;

@Component
@Slf4j
public class JwtUtil {

    private final JwtEncoder jwtEncoder;

    private final JwtEncoder coJwtEncoder;
    private final JwtDecoder jwtDecoder;

    private final JwtProperties jwtProperties;

    public JwtUtil(@Qualifier("jwtEncoder") JwtEncoder jwtEncoder, JwtDecoder jwtDecoder,@Qualifier("coJwtEncoder") JwtEncoder coJwtEncoder, JwtProperties jwtProperties) {
        this.jwtEncoder = jwtEncoder;
        this.coJwtEncoder = coJwtEncoder;
        this.jwtDecoder = jwtDecoder;
        this.jwtProperties = jwtProperties;
    }

    public String generateToken(Map<String, Object> attributes, String subject) {
        if (attributes == null){
            throw new IllegalArgumentException("attributes is null");
        }
        if (subject == null){
            throw new IllegalArgumentException("subject is null");
        }

        Instant now = Instant.now();
        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer("self")
                .issuedAt(now)
                .expiresAt(now.plus(jwtProperties.getExpiration(), ChronoUnit.DAYS))
                .subject(subject)
                .claims(claimsMap -> claimsMap.putAll(attributes))
                .build();

        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    public String generateCoToken(Map<String, Object> attributes, String subject) {
        if (attributes == null){
            throw new IllegalArgumentException("attributes is null");
        }
        if (subject == null){
            throw new IllegalArgumentException("subject is null");
        }

        Instant now = Instant.now();
        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer("self")
                .issuedAt(now)
                .expiresAt(now.plus(jwtProperties.getCoExpiration(), ChronoUnit.DAYS))
                .subject(subject)
                .claims(claimsMap -> claimsMap.putAll(attributes))
                .build();

        return coJwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    @Nullable
    public Jwt parseToken(String token) {
        try {
            return jwtDecoder.decode(token);
        } catch (JwtException e) {
            log.error("token：{},Token parse error",token, e);
            return null;
        }
    }
}
