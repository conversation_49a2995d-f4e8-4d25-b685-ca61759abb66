package com.unipus.digitalbook.common.utils;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Getter
public class FeishuUtil {

    private final WebClient webClient;

    public FeishuUtil(@Qualifier("loggingFilter") ExchangeFilterFunction exchangeFilterFunction,
                      @Value("${remote.feishu.webhook:https://open.feishu.cn/open-apis/bot/v2/hook/}") String webhook) {
        // 配置编码和解码
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 最大内存大小
                })
                .build();
        this.webClient = WebClient.builder()
            .exchangeStrategies(strategies)
            .baseUrl(webhook)
            .filter(exchangeFilterFunction)
            .filter((request, next) -> next.exchange(request)
                    .retryWhen(Retry.fixedDelay(3, Duration.ofSeconds(2)))) // 全局重试策略
            .build();
    }

    public void sendFeiShu(String key, String msg) {
        String realMsg = "【" + DateUtil.dateTimeFormat(new Date()) + "】" + msg;
        Map<String, Object> map = new HashMap<>(2);
        Map<String, String> textMap = new HashMap<>(1);
        textMap.put("text", realMsg);
        map.put("msg_type", "text");
        map.put("content", textMap);

        webClient.post()
            .uri(key)
            .header("Content-Type", "application/json")
            .bodyValue(map)
            .retrieve()
            .bodyToMono(String.class)
            .doOnSuccess(response -> System.out.println("消息发送成功: " + response))
            .doOnError(error -> System.err.println("消息发送失败: " + error.getMessage()))
            .subscribe();
    }

}
