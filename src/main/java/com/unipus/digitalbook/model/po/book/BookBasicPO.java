package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookBasic;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材基本信息表
 */
public class BookBasicPO implements Serializable {
    /**
     * 基本信息ID
     */
    private Long id;

    /**
     * 关联教材ID
     */
    private String bookId;

    /**
     * 教材中文名称
     */
    private String chineseName;

    /**
     * 教材英文名称
     */
    private String englishName;

    /**
     * 语种
     */
    private String language;

    /**
     * 教材业务类型
     */
    private String businessType;

    /**
     * 教程系列
     */
    private Long seriesId;

    /**
     * 对应课程
     */
    private String course;

    /**
     * 课程性质
     */
    private String courseNature;

    /**
     * 适用专业
     */
    private String applicableMajor;

    /**
     * 适用年级
     */
    private String applicableGrade;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * PC端封面图片地址
     */
    private String pcCoverUrl;

    /**
     * APP横版封面图片地址
     */
    private String appHorizontalCoverUrl;

    /**
     * APP竖版封面图片地址
     */
    private String appVerticalCoverUrl;

    /**
     * 浅色
     */
    private String lightColor;

    /**
     * 深色
     */
    private String darkColor;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 是否纯数字教材  0-否 1-是
     */
    private Boolean digitalFlag;

    public BookBasicPO() {
        super();
    }

    public BookBasicPO(Long basicId, Book book) {
        if (book == null) {
            return;
        }
        this.setId(basicId);
        this.setBookId(book.getId());
        this.setChineseName(book.getChineseName());
        this.setEnglishName(book.getEnglishName());
        this.setLanguage(book.getLanguage());
        this.setBusinessType(book.getBusinessType());
        this.setSeriesId(book.getSeriesId());
        this.setCourse(book.getCourse());
        this.setCourseNature(book.getCourseNature());
        this.setApplicableMajor(book.getApplicableMajor());
        this.setApplicableGrade(book.getApplicableGrade());
        this.setContactPhone(book.getContactPhone());
        this.setContactEmail(book.getContactEmail());
        this.setPcCoverUrl(book.getPcCoverUrl());
        this.setAppHorizontalCoverUrl(book.getAppHorizontalCoverUrl());
        this.setAppVerticalCoverUrl(book.getAppVerticalCoverUrl());
        this.setLightColor(book.getLightColor());
        this.setDarkColor(book.getDarkColor());
        this.setCreateTime(book.getCreateTime());
        this.setUpdateTime(book.getUpdateTime());
        this.setCreateBy(book.getCreateBy());
        this.setUpdateBy(book.getUpdateBy());
        this.setEnable(book.getEnable());
        this.setDigitalFlag(book.getDigitalFlag());
    }

    public void fromEntity(Book book) {
        if (book == null) {
            return;
        }
        this.setBookId(book.getId());
        this.setChineseName(book.getChineseName());
        this.setEnglishName(book.getEnglishName());
        this.setLanguage(book.getLanguage());
        this.setBusinessType(book.getBusinessType());
        this.setSeriesId(book.getSeriesId());
        this.setCourse(book.getCourse());
        this.setCourseNature(book.getCourseNature());
        this.setApplicableMajor(book.getApplicableMajor());
        this.setApplicableGrade(book.getApplicableGrade());
        this.setContactPhone(book.getContactPhone());
        this.setContactEmail(book.getContactEmail());
        this.setPcCoverUrl(book.getPcCoverUrl());
        this.setAppHorizontalCoverUrl(book.getAppHorizontalCoverUrl());
        this.setAppVerticalCoverUrl(book.getAppVerticalCoverUrl());
        this.setCreateTime(book.getCreateTime());
        this.setUpdateTime(book.getUpdateTime());
        this.setCreateBy(book.getCreateBy());
        this.setUpdateBy(book.getUpdateBy());
        this.setEnable(true);
        this.setDigitalFlag(book.getDigitalFlag());
    }

    public BookBasic toEntity() {
        BookBasic bookBasic = new BookBasic();
        bookBasic.setId(this.getId());
        bookBasic.setBookId(this.getBookId());
        bookBasic.setChineseName(this.getChineseName());
        bookBasic.setEnglishName(this.getEnglishName());
        bookBasic.setLanguage(this.getLanguage());
        bookBasic.setBusinessType(this.getBusinessType());
        bookBasic.setSeriesId(this.getSeriesId());
        bookBasic.setCourse(this.getCourse());
        bookBasic.setCourseNature(this.getCourseNature());
        bookBasic.setApplicableMajor(this.getApplicableMajor());
        bookBasic.setApplicableGrade(this.getApplicableGrade());
        bookBasic.setContactPhone(this.getContactPhone());
        bookBasic.setContactEmail(this.getContactEmail());
        bookBasic.setPcCoverUrl(this.getPcCoverUrl());
        bookBasic.setAppHorizontalCoverUrl(this.getAppHorizontalCoverUrl());
        bookBasic.setAppVerticalCoverUrl(this.getAppVerticalCoverUrl());
        bookBasic.setLightColor(this.getLightColor());
        bookBasic.setDarkColor(this.getDarkColor());
        bookBasic.setVersionNumber(this.getVersionNumber());
        bookBasic.setCreateTime(this.getCreateTime());
        bookBasic.setUpdateTime(this.getUpdateTime());
        bookBasic.setCreateBy(this.getCreateBy());
        bookBasic.setUpdateBy(this.getUpdateBy());
        bookBasic.setEnable(this.getEnable());
        bookBasic.setDigitalFlag(this.getDigitalFlag());
        return bookBasic;
    }

    public BookBasicPO fromEntity(BookBasic bookBasic) {
        return new BookBasicPO(bookBasic);
    }

    //todo  完成类型转换
    private BookBasicPO(BookBasic bookBasic) {
        if (bookBasic != null) {
            this.id = bookBasic.getId();
            this.bookId = bookBasic.getBookId();
            this.chineseName = bookBasic.getChineseName();
            this.englishName = bookBasic.getEnglishName();
            this.language = bookBasic.getLanguage();
            this.businessType = bookBasic.getBusinessType();
            this.seriesId = bookBasic.getSeriesId();
            this.course = bookBasic.getCourse();
            this.courseNature = bookBasic.getCourseNature();
            this.applicableMajor = bookBasic.getApplicableMajor();
            this.applicableGrade = bookBasic.getApplicableGrade();
            this.contactPhone = bookBasic.getContactPhone();
            this.contactEmail = bookBasic.getContactEmail();
            this.pcCoverUrl = bookBasic.getPcCoverUrl();
            this.appHorizontalCoverUrl = bookBasic.getAppHorizontalCoverUrl();
            this.appVerticalCoverUrl = bookBasic.getAppVerticalCoverUrl();
            this.lightColor = bookBasic.getLightColor();
            this.darkColor = bookBasic.getDarkColor();
            this.versionNumber = bookBasic.getVersionNumber();
            this.createTime = bookBasic.getCreateTime();
            this.updateTime = bookBasic.getUpdateTime();
            this.createBy = bookBasic.getCreateBy();
            this.updateBy = bookBasic.getUpdateBy();
            this.enable = bookBasic.getEnable();
            this.digitalFlag = bookBasic.getDigitalFlag();
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Long getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Long seriesId) {
        this.seriesId = seriesId;
    }

    public String getCourse() {
        return course;
    }

    public void setCourse(String course) {
        this.course = course;
    }

    public String getCourseNature() {
        return courseNature;
    }

    public void setCourseNature(String courseNature) {
        this.courseNature = courseNature;
    }

    public String getApplicableMajor() {
        return applicableMajor;
    }

    public void setApplicableMajor(String applicableMajor) {
        this.applicableMajor = applicableMajor;
    }

    public String getApplicableGrade() {
        return applicableGrade;
    }

    public void setApplicableGrade(String applicableGrade) {
        this.applicableGrade = applicableGrade;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public String getLightColor() {
        return lightColor;
    }

    public void setLightColor(String lightColor) {
        this.lightColor = lightColor;
    }

    public String getDarkColor() {
        return darkColor;
    }

    public void setDarkColor(String darkColor) {
        this.darkColor = darkColor;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Boolean getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        this.digitalFlag = digitalFlag;
    }
}