package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.UserPaperInfo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成绩提交批次实体类
 */
public class PaperScoreBatchPO {
    // 成绩提交批次ID（主键ID）
    private String id;
    // 试卷ID（题组业务ID）
    private String paperId;
    // 试卷版本号
    private String paperVersionNumber;
    // 试卷类型（1：常规卷/2：挑战卷/3：诊断卷）
    private Integer paperType;
    // 租户ID
    private Long tenantId;
    // 用户openId
    private String openId;
    // 试卷用户分数
    private BigDecimal userScore;
    // 标准分数
    private BigDecimal standardScore;
    // 成绩提交状态（0：未提交/1：已提交）
    private Integer status;
    // 成绩提交时间
    private Date scoreSubmitDate;
    // 创建时间
    private Date createTime;
    // 最后更新时间
    private Date updateTime;
    // 创建者ID
    private String createBy;
    // 最后更新者ID
    private String updateBy;
    // 是否有效（0-无效/1-有效）
    private Boolean enable;

    public PaperScoreBatchPO() {}

    // 构建检索条件用
    public PaperScoreBatchPO(String paperId, String versionNumber, String openId, Long tenantId, Integer status) {
        this.paperId = paperId;
        this.paperVersionNumber = versionNumber;
        this.openId = openId;
        this.tenantId = tenantId;
        this.status = status;
    }

    // 构建数据存储用
    public PaperScoreBatchPO(PaperInstance paperInstance, BigDecimal userScore, Integer status, Date scoreSubmitDate,
                             String openId, Long tenantId){
        this.id = paperInstance.getScoreBatchId();
        this.paperId = paperInstance.getPaperId();
        this.paperVersionNumber = paperInstance.getVersionNumber();
        this.paperType = paperInstance.getPaperType().getCode();
        this.tenantId = tenantId;
        this.openId = openId;
        this.userScore = userScore;
        this.standardScore = paperInstance.getTotalScore();
        this.status = status;
        this.scoreSubmitDate = scoreSubmitDate;
        this.createBy = openId;
        this.updateBy = openId;
        this.enable = true;
    }

    // 数据更新用(更新提交状态和提交时间)
    public PaperScoreBatchPO(UserPaperInfo userPaperInfo) {
        // 基于上面的注释内容生成
        this.id = userPaperInfo.getScoreBatchId();
        this.userScore = userPaperInfo.getUserScore();
        this.status = userPaperInfo.getStatus();
        this.scoreSubmitDate = new Date();
        this.updateBy = userPaperInfo.getOpenId();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperVersionNumber() {
        return paperVersionNumber;
    }

    public void setPaperVersionNumber(String paperVersionNumber) {
        this.paperVersionNumber = paperVersionNumber;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getScoreSubmitDate() {
        return scoreSubmitDate;
    }

    public void setScoreSubmitDate(Date scoreSubmitDate) {
        this.scoreSubmitDate = scoreSubmitDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
