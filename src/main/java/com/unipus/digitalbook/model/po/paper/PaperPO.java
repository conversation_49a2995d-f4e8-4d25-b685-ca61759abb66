package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;

import java.util.Date;

/**
 * 试卷持久化对象
 */
public class PaperPO {
    // 试卷主键ID
    private Long id;
    // 教材ID
    private String bookId;
    // 章节ID
    private String chapterId;
    // 试卷ID
    private String paperId;
    // 试卷名称
    private String paperName;
    // 试卷类型
    private Integer questionGroupType;
    // 试卷说明
    private String description;
    // 试卷内容
    private String content;
    // 题目数量
    private Integer questionCount;
    // 试卷总分
    private Integer totalScore;
    // 创建时间
    private Date createTime;
    // 创建人
    private Long createBy;
    // 试卷版本
    private String versionNumber;

    public Paper toEntity() {
        return toEntity(null, null, null);
    }

    public Paper toEntity(String userName, Boolean isReferenced, Boolean isPublished){
        Paper paper = new Paper();
        paper.setId(this.id);
        paper.setBookId(this.bookId);
        paper.setPaperId(this.paperId);
        paper.setPaperType(PaperTypeEnum.getTypeByGroupCode(this.questionGroupType));
        paper.setPaperName(this.paperName);
        paper.setDescription(this.description);
        paper.setContent(this.content);
        paper.setQuestionCount(this.questionCount);
        paper.setTotalScore(this.totalScore);
        paper.setCreateTime(this.createTime);
        paper.setCreatorId(this.createBy);
        paper.setVersionNumber(this.versionNumber);
        paper.setCreatorName(userName==null ? "": userName);
        paper.setReferenceFlag(isReferenced==null ? Boolean.FALSE: isReferenced);
        paper.setPublishFlag(isPublished==null ? Boolean.FALSE: isPublished);
        paper.setQuestionCount(this.questionCount);
        return paper;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Integer getQuestionGroupType() {
        return questionGroupType;
    }

    public void setQuestionGroupType(Integer questionGroupType) {
        this.questionGroupType = questionGroupType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}