package com.unipus.digitalbook.model.po.tenant;

import com.unipus.digitalbook.model.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantSubscribePO extends BasePO {

    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 消息主题
     */
    private String messageTopic;

    /**
     * kafka主题
     */
    private String kafkaTopic;

    /**
     * kafka集群地址
     */
    private String kafkaBootstrapServers;

    /**
     * http调用地址
     */
    private String httpUrl;

    public TenantSubscribePO() {}
}
