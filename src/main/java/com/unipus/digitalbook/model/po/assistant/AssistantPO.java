package com.unipus.digitalbook.model.po.assistant;

import com.unipus.digitalbook.model.params.assistant.AssistantUpdateParam;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

@Data
public class AssistantPO implements Serializable {

    /**
     * 数字人助手id
     */
    private String id;

    /**
     * 数字人助手名称
     */
    private String name;

    /**
     * 数字人助手配置类型
     */
    private Integer configType;

    /**
     * 数字人助教类型
     */
    private Integer type;

    /**
     * 关联教材id
     */
    private String bookId;

    /**
     * 关联章节id
     */
    private String chapterId;

    /**
     * 关联块ids
     */
    private String blockIds;

    /**
     * 关联cms模板id
     */
    private String templateId;

    /**
     * 自定义模板json
     */
    private String templateJson;

    /**
     * 关联cms数字人助手id
     */
    private String cmsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效
     */
    private Boolean enable;

    public AssistantVersionPO toVersion(String version) {
        AssistantVersionPO assistantVersionPO = new AssistantVersionPO();
        assistantVersionPO.setId(getId());
        assistantVersionPO.setVersion(version);
        assistantVersionPO.setBlockIds(getBlockIds());
        assistantVersionPO.setBookId(getBookId());
        assistantVersionPO.setChapterId(getChapterId());
        assistantVersionPO.setCmsId(getCmsId());
        assistantVersionPO.setName(getName());
        assistantVersionPO.setEnable(getEnable());
        assistantVersionPO.setType(getType());
        assistantVersionPO.setConfigType(getConfigType());
        assistantVersionPO.setTemplateId(getTemplateId());
        assistantVersionPO.setTemplateJson(getTemplateJson());
        assistantVersionPO.setCreateBy(getCreateBy());
        assistantVersionPO.setUpdateBy(getUpdateBy());
        assistantVersionPO.setCreateTime(getCreateTime());
        assistantVersionPO.setUpdateTime(getUpdateTime());
        return assistantVersionPO;
    }

    public AssistantPO fromUpdateParam(AssistantUpdateParam param, Long currentUserId) {
        this.name = Optional.ofNullable(param.getName()).orElse("");
        this.templateId = param.getTemplateId();
        this.templateJson = param.getTemplateJson();
        this.type = param.getType();
        this.configType = param.getConfigType();
        this.updateBy = currentUserId;
        this.updateTime = new Date();
        return this;
    }
}
