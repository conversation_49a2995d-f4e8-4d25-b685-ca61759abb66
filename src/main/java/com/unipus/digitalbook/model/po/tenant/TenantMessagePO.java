package com.unipus.digitalbook.model.po.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.tenant.TenantMessage;
import com.unipus.digitalbook.model.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantMessagePO extends BasePO {
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 消息种类
     */
    private String messageTopic;

    /**
     * 消息
     */
    private String message;

    /**
     * 消息唯一标识
     */
    private String messageUuid;

    /**
     * 消息是否异步
     */
    private Boolean async;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    public <S> TenantMessage<S> toEntity(TypeReference<S> typeRef) {
        TenantMessage<S> tenantMessage = new TenantMessage<>();
        tenantMessage.setId(id);
        tenantMessage.setMessage(message);
        tenantMessage.setMessageTopic(messageTopic);
        tenantMessage.setMessageUuid(messageUuid);
        tenantMessage.setTenantId(tenantId);
        tenantMessage.setMessageObj(JsonUtil.readValue(message, typeRef));
        tenantMessage.setCreateBy(getCreateBy());
        tenantMessage.setCreateTime(getCreateTime());
        tenantMessage.setEnable(getEnable());
        tenantMessage.setUpdateBy(getUpdateBy());
        tenantMessage.setUpdateTime(getUpdateTime());
        tenantMessage.setAsync(getAsync());
        return tenantMessage;
    }

}
