package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.QuestionBank;

import java.util.Date;

/**
 * 试卷管理实体类PO
 */
public class QuestionBankPO {
    // 题库主键ID
    private Long id;
    // 试卷ID
    private String paperId;
    // 题库ID
    private String bankId;
    // 题库名称
    private String bankName;
    // 题目数量
    private Integer questionCount;
    // 每轮题目数量
    private Integer questionCountPerRound;
    // 每题分数
    private Integer questionScore;
    // 创建时间
    private Date createTime;
    // 创建人
    private Long createBy;
    // 试卷版本
    private String versionNum;

    public QuestionBank toEntity(String paperId) {
        return toEntity(paperId, null);
    }

    public QuestionBank toEntity(String paperId, String userName) {
        QuestionBank questionBank = new QuestionBank();
        questionBank.setId(this.id);
        questionBank.setPaperId(paperId);
        questionBank.setBankId(this.bankId);
        questionBank.setBankName(this.bankName);
        questionBank.setQuestionCount(this.questionCount);
        questionBank.setQuestionScore(this.questionScore);
        questionBank.setQuestionsPerRound(this.questionCountPerRound);
        questionBank.setCreateTime(this.createTime);
        questionBank.setCreatorId(this.createBy);
        questionBank.setCreatorName(userName);
        questionBank.setVersionNumber(this.versionNum);
        return questionBank;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getQuestionCountPerRound() {
        return questionCountPerRound;
    }

    public void setQuestionCountPerRound(Integer questionCountPerRound) {
        this.questionCountPerRound = questionCountPerRound;
    }

    public Integer getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Integer questionScore) {
        this.questionScore = questionScore;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

}