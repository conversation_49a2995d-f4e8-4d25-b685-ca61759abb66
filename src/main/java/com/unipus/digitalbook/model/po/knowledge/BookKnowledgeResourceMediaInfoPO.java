package com.unipus.digitalbook.model.po.knowledge;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName book_knowledge_resource_media_info
 */
@Data
public class BookKnowledgeResourceMediaInfoPO implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 所属资源id
     */
    private Long resourceId;
    /**
     * 所属资源详情id
     */
    private Long resourceDetailId;

    /**
     * 视频开始时间
     */
    private Integer startTime;

    /**
     * 视频开始的帧图
     */
    private String startPictureUrl;

    /**
     * 视频唯一标识
     */
    private String multimediaKey;

    /**
     * 
     */
    private String multimediaIndex;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    private static final long serialVersionUID = 1L;
}