package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.Theme;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 教材主题表
 */
public class ThemePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主题ID
     */
    private Long id;
    /**
     * 主题名称
     */
    private String name;
    /**
     * 主题内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 是否有效
     */
    private Boolean enable;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Theme toEntity() {
        Theme theme = new Theme();
        theme.setId(id);
        theme.setName(name);
        theme.setContent(content);
        theme.setCreateTime(createTime);
        theme.setUpdateTime(updateTime);
        theme.setCreateBy(createBy);
        theme.setUpdateBy(updateBy);
        theme.setEnable(enable);
        return theme;
    }

    public static ThemePO build(Theme theme, Long userId) {
        ThemePO themePO = new ThemePO();
        themePO.setId(theme.getId());
        themePO.setName(theme.getName());
        themePO.setContent(theme.getContent());
        themePO.setCreateBy(userId);
        themePO.setUpdateBy(userId);
        themePO.setEnable(theme.getEnable());
        return themePO;
    }
}