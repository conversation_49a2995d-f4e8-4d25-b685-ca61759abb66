package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 第三方用户表
 *
 * @TableName third_party_user_info
 */
@Data
public class ThirdPartyUserInfoPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 用户OPENID
     */
    private String openId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户全名
     */
    private String fullName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    @Serial
    private static final long serialVersionUID = 1L;

    public ThirdPartyUserInfo toEntity() {
        ThirdPartyUserInfo thirdPartyUserInfo = new ThirdPartyUserInfo();
        BeanUtils.copyProperties(this, thirdPartyUserInfo);
        return thirdPartyUserInfo;
    }

    public static ThirdPartyUserInfoPO fromEntity(ThirdPartyUserInfo thirdPartyUserInfo) {
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = new ThirdPartyUserInfoPO();
        BeanUtils.copyProperties(thirdPartyUserInfo, thirdPartyUserInfoPO);
        return thirdPartyUserInfoPO;
    }
}