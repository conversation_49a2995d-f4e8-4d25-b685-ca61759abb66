package com.unipus.digitalbook.model.po.menu;

import com.unipus.digitalbook.model.entity.menu.Menu;
import com.unipus.digitalbook.model.enums.StatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单表
 * @TableName menu
 */
public class MenuPO implements Serializable {
    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父级菜单ID
     */
    private Long parentId;

    /**
     * 菜单名字
     */
    private String name;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 菜单位置
     */
    private Integer position;

    /**
     * 启用状态，1表示启用，0表示禁用
     */
    private Integer status;

    /**
     * 是否生效：true生效，false禁用
     */
    private Boolean enable;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    public MenuPO(){}
    public MenuPO(Menu menu) {
        this.setName(menu.getName());
        this.setPath(menu.getPath());
        this.setParentId(menu.getParentId());
        this.setEnable(true);
        this.setId(menu.getId());
        this.setStatus(StatusEnum.ENABLE.getCode());
        this.setCreateBy(menu.getCreateBy());
        this.setUpdateBy(menu.getUpdateBy());
    }
    public Menu toMenu() {
        Menu menu = new Menu();
        menu.setId(this.getId());
        menu.setName(this.getName());
        menu.setPath(this.getPath());
        menu.setPosition(this.getPosition());
        menu.setStatus(this.getStatus());
        menu.setParentId(this.getParentId());
        menu.setCreateBy(this.getCreateBy());
        menu.setUpdateBy(this.getUpdateBy());
        return menu;
    }
    /**
     * 菜单ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 菜单ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 父级菜单ID
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 父级菜单ID
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * 菜单名字
     */
    public String getName() {
        return name;
    }

    /**
     * 菜单名字
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 菜单路径
     */
    public String getPath() {
        return path;
    }

    /**
     * 菜单路径
     */
    public void setPath(String path) {
        this.path = path;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    /**
     * 启用状态，1表示启用，0表示禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 启用状态，1表示启用，0表示禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 是否生效：true生效，false禁用
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否生效：true生效，false禁用
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }
}