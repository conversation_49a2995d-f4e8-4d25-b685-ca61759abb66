package com.unipus.digitalbook.model.po.tag;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.tag.TagResource;

import java.time.LocalDateTime;

public class TagReferencePO {
    // 主键ID
    private Long id;
    // 资源ID
    private String resourceId;

    /**
     * 资源版本
     */
    private String resourceVersion;

    /**
     * 资源类型
     * 1 题目
     */
    private Integer resourceType;

    // 标签ID，关联标签表
    private Long tagId;
    // 创建时间
    private LocalDateTime createTime;
    // 更新时间
    private LocalDateTime updateTime;
    // 创建人ID
    private Long createBy;
    // 更新人ID
    private Long updateBy;
    // 是否启用，true表示启用，false表示禁用
    private Boolean enable;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    public TagReferencePO() {}

    public TagReferencePO(String resourceId, Integer tagResourceType, Long tagId, Long userId) {
        this.resourceId = resourceId;
        this.tagId = tagId;
        this.createBy = userId;
        this.updateBy = userId;
        this.resourceType = tagResourceType;
        this.resourceVersion = IdentifierUtil.DEFAULT_VERSION_NUMBER;
    }

    public TagReferencePO(TagResource resource, Long tagId, Integer sortOrder, Long userId) {
        this.resourceId = resource.getResourceId();
        this.resourceVersion = resource.getResourceVersion();
        this.resourceType = resource.getResourceType();
        this.tagId = tagId;
        this.sortOrder = sortOrder;
        this.createBy = userId;
        this.updateBy = userId;
    }

    // 获取主键ID
    public Long getId() {
        return id;
    }

    // 设置主键ID
    public void setId(Long id) {
        this.id = id;
    }

    // 获取资源ID
    public String getResourceId() {
        return resourceId;
    }

    // 设置资源ID
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    // 获取标签ID
    public Long getTagId() {
        return tagId;
    }

    // 设置标签ID
    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    // 获取创建时间
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    // 设置创建时间
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    // 获取更新时间
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    // 设置更新时间
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    // 获取创建人ID
    public Long getCreateBy() {
        return createBy;
    }

    // 设置创建人ID
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    // 获取更新人ID
    public Long getUpdateBy() {
        return updateBy;
    }

    // 设置更新人ID
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    // 获取是否启用状态
    public Boolean getEnable() {
        return enable;
    }

    // 设置是否启用状态
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getResourceVersion() {
        return resourceVersion;
    }

    public void setResourceVersion(String resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // 重写toString方法，方便打印对象信息
    @Override
    public String toString() {
        return "TagReference{" +
                "id=" + id +
                ", resourceId='" + resourceId + '\'' +
                ", resourceType=" + resourceType +
                ", resourceVersion='" + resourceVersion + '\'' +
                ", tagId=" + tagId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                ", enable=" + enable +
                '}';
    }
}
