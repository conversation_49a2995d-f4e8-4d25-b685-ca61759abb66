package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.enums.PaperInstanceRelationTypeEnum;

import java.util.Date;

/**
 * PaperInstanceRelationPO
 * @TableName paper_instance_relation
 */
public class PaperInstanceRelationPO {
    private Long id;
    private String paperId;
    private String paperVersionNumber;
    private String baseInstanceId;
    private String targetInstanceId;
    private Integer type;
    private Long tenantId;
    private String openId;
    private Date createTime;
    private Date updateTime;
    private Boolean enable;

    public PaperInstanceRelationPO() {}

    public PaperInstanceRelationPO(PaperInstance paperInstance) {
        this.paperId = paperInstance.getPaperId();
        this.paperVersionNumber = paperInstance.getVersionNumber();
        this.baseInstanceId = paperInstance.getInstanceId();
        this.type = PaperInstanceRelationTypeEnum.RECOMMEND.getCode();
        this.tenantId = paperInstance.getTenantId();
        this.openId = paperInstance.getOpenId();
        this.targetInstanceId = null;
    }

    public PaperInstanceRelationPO(String baseInstanceId, PaperInstance paperInstance) {
        this.baseInstanceId = baseInstanceId;
        this.targetInstanceId = paperInstance.getInstanceId();
        this.tenantId = paperInstance.getTenantId();
        this.type = PaperInstanceRelationTypeEnum.RECOMMEND.getCode();
        this.tenantId = paperInstance.getTenantId();
        this.openId = paperInstance.getOpenId();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperVersionNumber() {
        return paperVersionNumber;
    }

    public void setPaperVersionNumber(String paperVersionNumber) {
        this.paperVersionNumber = paperVersionNumber;
    }

    public String getBaseInstanceId() {
        return baseInstanceId;
    }

    public void setBaseInstanceId(String baseInstanceId) {
        this.baseInstanceId = baseInstanceId;
    }

    public String getTargetInstanceId() {
        return targetInstanceId;
    }

    public void setTargetInstanceId(String targetInstanceId) {
        this.targetInstanceId = targetInstanceId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
