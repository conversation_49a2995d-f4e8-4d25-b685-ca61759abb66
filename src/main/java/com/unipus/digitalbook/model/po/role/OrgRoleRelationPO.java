package com.unipus.digitalbook.model.po.role;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织与角色关系表
 * @TableName org_role_relation
 */
public class OrgRoleRelationPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 是否生效：true生效，false禁用
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新人
     */
    private Long updateBy;

    public OrgRoleRelationPO() {}

    public OrgRoleRelationPO(Long orgId, Long roleId, Long userId, boolean enable) {
        this.orgId = orgId;
        this.roleId = roleId;
        this.createBy = userId;
        this.updateBy = userId;
        this.enable = enable;
    }
    /**
     * 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 组织ID
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 组织ID
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 角色ID
     */
    public Long getRoleId() {
        return roleId;
    }

    /**
     * 角色ID
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 是否生效：true生效，false禁用
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否生效：true生效，false禁用
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }
}