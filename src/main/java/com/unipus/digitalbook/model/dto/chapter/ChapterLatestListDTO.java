package com.unipus.digitalbook.model.dto.chapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "教材章节列表")
@Data
public class ChapterLatestListDTO implements Serializable {

    @Schema(description = "章节列表")
    private List<ChapterLatestDTO> chapterLatestList;

    @Data
    public static class ChapterLatestDTO implements Serializable {
        @Schema(description = "章节ID")
        private String id;

        @Schema(description = "关联教材ID")
        private String bookId;

        @Schema(description = "章节编号")
        private Integer chapterNumber;

        @Schema(description = "章节名称")
        private String name;

        @Schema(description = " 版本号，标识章节的具体版本。")
        private String versionNumber;


    }
}
