package com.unipus.digitalbook.model.dto.chapter;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;


@Schema(description = "教材章节")
public class ChapterWithContentDTO implements Serializable {
    @Schema(description = "章节ID")
    private String id;

    @Schema(description = "关联教材ID")
    private String bookId;

    @Schema(description = "章节编号")
    private Integer chapterNumber;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "章节头图")
    private String headerImg;

    @Schema(description = "章节内容")
    private String content;

    @Schema(description = "章节版本ID")
    private Long versionId;

    @Schema(description = "章节版本号")
    private String versionNumber;

    @Schema(description = "目录")
    private String catalog;

    @Schema(description = "学生内容")
    private String studentContent;

    @Schema(description = "资源")
    private String resource;

    public void fillChapterInfo(ChapterPO chapter){
        this.id = chapter.getId();
        this.bookId = chapter.getBookId();
        this.chapterNumber = chapter.getChapterNumber();
        this.name = chapter.getName();
    }
    public void fillChapterInfo(Chapter chapter){
        this.id = chapter.getId();
        this.bookId = chapter.getBookId();
        this.chapterNumber = chapter.getChapterNumber();
        this.name = chapter.getName();
    }

    public  void fillVersionInfo(ChapterVersionPO version){
        this.versionId = version.getId();
        this.versionNumber = version.getVersionNumber();
        this.content = version.getContent();
    }

    public  void fillVersionInfo(ChapterVersion version){
        this.versionId = version.getId();
        this.versionNumber = version.getVersionNumber();
        this.content = version.getContent();
        this.catalog = JSON.toJSONString(version.getHeaderNodeList());
        this.studentContent = version.getStudentContent();
        this.resource = version.getResource();
        this.headerImg = version.getHeaderImg();
    }

    public  void fillVersionInfoWithReaderType(ChapterVersion version, ReaderTypeEnum readerType){
        this.versionId = version.getId();
        this.versionNumber = version.getVersionNumber();
        this.content = ReaderTypeEnum.STUDENT.equals(readerType) ? version.getStudentContent() : version.getContent();
        this.catalog = JSON.toJSONString(version.getHeaderNodeList());
        this.resource = version.getResource();
        this.headerImg = version.getHeaderImg();
    }

    public ChapterWithContentDTO(){
        super();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public void fromEntity(Chapter chapter){
        this.fillChapterInfo(chapter);
        if (chapter.getChapterVersion() != null) {
            this.fillVersionInfo(chapter.getChapterVersion());
        }
    }
}
