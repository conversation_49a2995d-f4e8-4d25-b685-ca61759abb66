package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.io.Serializable;

@Getter
public class BookStatusStatisticDTO implements Serializable {
    @Schema(description = "状态名称", example = "编辑中")
    String statusName;
    @Schema(description = "状态码")
    Integer status;
    @Schema(description = "当前状态下的数量", example = "99")
    Integer num;

    public BookStatusStatisticDTO setStatusName(String statusName) {
        this.statusName = statusName;
        return this;
    }

    public BookStatusStatisticDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public BookStatusStatisticDTO setNum(Integer num) {
        this.num = num;
        return this;
    }
}
