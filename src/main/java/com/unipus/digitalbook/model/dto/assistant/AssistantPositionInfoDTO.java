package com.unipus.digitalbook.model.dto.assistant;

import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantLatestInstanceResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "数字人信息")
@Data
public class AssistantPositionInfoDTO extends AssistantLatestInstanceResponse{

    @Schema(description = "块信息")
    private List<String> blockIds;

    public AssistantPositionInfoDTO(AssistantLatestInstanceResponse response, List<String> blockIds) {
        this.setAssistantId(response.getAssistantId());
        this.setAssistantInstanceId(response.getAssistantInstanceId());
        this.setConfig(response.getConfig());
        this.setScene(response.getScene());
        this.setTitle(response.getTitle());
        this.setPlatform(response.getPlatform());
        this.setEntityId(response.getEntityId());
        this.setSearchFlag(response.getSearchFlag());
        this.setBlockIds(blockIds);
        this.setBusinessFlag(response.getBusinessFlag());
    }

}
