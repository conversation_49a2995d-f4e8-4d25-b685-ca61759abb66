package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookChangeItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.StringJoiner;
@Schema(description = "当前版本更改的内容数据")
public class BookVersionChangeDTO implements Serializable {
    @Schema(description = "资源类型编码: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节")
    private Integer typeCode;

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "资源版本id")
    private Long versionId;

    public BookVersionChangeDTO(Integer typeCode, String resourceId, Long versionId) {
        this.typeCode = typeCode;
        this.resourceId = resourceId;
        this.versionId = versionId;
    }

    public BookVersionChangeDTO(BookChangeItemEntity changeItemEntity) {
        this.typeCode = changeItemEntity.getTypeCode();
        this.resourceId = changeItemEntity.getResourceId();
        this.versionId = changeItemEntity.getVersionId();
    }

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BookVersionChangeDTO.class.getSimpleName() + "[", "]")
                .add("typeCode=" + typeCode)
                .add("resourceId='" + resourceId + "'")
                .add("versionId=" + versionId)
                .toString();
    }
}
