package com.unipus.digitalbook.model.dto.complement;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "配套资源")
public class ComplementResourceDTO implements Serializable {
    @Schema(description = "资源id")
    private String id;

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "媒体后缀")
    private String suffix;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "地址")
    private String resourceUrl;

    @Schema(description = "媒体大小,byte")
    private Long size;

    @Schema(description = "媒体时长")
    private Long duration;

    @Schema(description = "上传时间")
    private Long uploadTime;

    @Schema(description = "媒体可见状态 0-全部可见 1-仅教师可见")
    private Integer visibleStatus;

    @Schema(description = "封面图地址")
    private String coverUrl;

    public ComplementResourceDTO() {
    }
    public ComplementResourceDTO(ComplementResource complementResource) {
        this.id = complementResource.getResourceId();
        this.bookId = complementResource.getBookId();
        this.suffix = complementResource.getSuffix();
        this.name = complementResource.getName();
        this.resourceUrl = complementResource.getResourceUrl();
        this.size = complementResource.getSize();
        this.duration = complementResource.getDuration();
        this.visibleStatus = complementResource.getVisibleStatus();
        this.coverUrl = complementResource.getCoverUrl();
        this.uploadTime = complementResource.getUploadTime().getTime();
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Integer getVisibleStatus() {
        return visibleStatus;
    }

    public void setVisibleStatus(Integer visibleStatus) {
        this.visibleStatus = visibleStatus;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }
}
