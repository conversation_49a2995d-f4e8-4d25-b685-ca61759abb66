package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class ChapterResourceDTO implements Serializable {
    @Schema(description = "教材章节ID")
    private String chapterId;

    @Schema(description = "教材章节资源")
    private String resource;

    public ChapterResourceDTO() {
    }
    public ChapterResourceDTO(ChapterVersion chapterVersion) {
        this.chapterId = chapterVersion.getChapterId();
        this.resource = chapterVersion.getResource();
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }
}
