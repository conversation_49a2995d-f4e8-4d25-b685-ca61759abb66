package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookSettingLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "教材发布日志")
@Data
public class BookSettingLogDTO implements Serializable {

    @Schema(description = "操作人", example = "张三")
    private String userName;

    @Schema(description = "发布时间", example = "1630000000000")
    private Long createTime;

    @Schema(description = "发布内容", example = "发布数字人")
    private String content;

    public static BookSettingLogDTO fromEntity(BookSettingLog bookSettingLog) {
        BookSettingLogDTO dto = new BookSettingLogDTO();
        dto.setContent(bookSettingLog.getContent());
        dto.setCreateTime(bookSettingLog.getPublishTime().getTime());
        dto.setUserName(bookSettingLog.getOperator());
        return dto;
    }

}
