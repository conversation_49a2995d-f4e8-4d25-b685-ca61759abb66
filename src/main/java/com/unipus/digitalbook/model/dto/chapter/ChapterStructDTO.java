package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.service.converter.HeaderNodeToChapterTreeConverter;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "章节数据的传输对象")
public class ChapterStructDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "章节唯一标识符", example = "12345")
    private String chapterId;

    @Schema(description = "章节名称", example = "第一章")
    private String chapterName;

    @Schema(description = "版本号", example = "txgnl")
    private String versionNumber;

    @Schema(description = "在书内的排序", example = "1")
    private Integer number;

    @Schema(description = "章节内容的节点列表，根节点下的一级节点列表")
    private List<ChapterNodeDTO> allChapterNodeList;

    @Schema(description = "章节目录的节点树")
    private List<ChapterNodeDTO> headerNodeTree;

    @Schema(description = "章节内容的节点树")
    private List<ChapterNodeDTO> allNodeTree;

    @Schema(description = "章节内的题目数据")
    private List<BigQuestionGroupDTO> questionGroupList;


    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public List<ChapterNodeDTO> getAllChapterNodeList() {
        return allChapterNodeList;
    }

    public void setAllChapterNodeList(List<ChapterNodeDTO> allChapterNodeList) {
        this.allChapterNodeList = allChapterNodeList;
    }

    public List<ChapterNodeDTO> getHeaderNodeTree() {
        return headerNodeTree;
    }

    public void setHeaderNodeTree(List<ChapterNodeDTO> headerNodeTree) {
        this.headerNodeTree = headerNodeTree;
    }

    public List<ChapterNodeDTO> getAllNodeTree() {
        return allNodeTree;
    }

    public void setAllNodeTree(List<ChapterNodeDTO> allNodeTree) {
        this.allNodeTree = allNodeTree;
    }

    public List<BigQuestionGroupDTO> getQuestionGroupList() {
        return questionGroupList;
    }

    public void setQuestionGroupList(List<BigQuestionGroupDTO> questionGroupList) {
        this.questionGroupList = questionGroupList;
    }


    public ChapterStructDTO() {
        super();
    }

    public ChapterStructDTO(Chapter chapter) {
        this.chapterId = chapter.getId();
        this.chapterName = chapter.getName();
        this.versionNumber = chapter.getVersionNumber();
        this.number = chapter.getChapterNumber();
        this.headerNodeTree = HeaderNodeToChapterTreeConverter.convertToChapterNodeTree(chapter.getChapterVersion().getHeaderNodeList());
        if (chapter.getChapterVersion() != null && chapter.getChapterVersion().getChapterNodeList() != null) {
            this.allChapterNodeList = chapter.getChapterVersion().getChapterNodeList().stream().map(ChapterNodeDTO::new).toList();
            this.allNodeTree = chapter.getChapterVersion().getChapterNodeTree().stream().map(ChapterNodeDTO::new).toList();
            this.questionGroupList = chapter.getChapterVersion().getQuestionList().stream().map(q -> new BigQuestionGroupDTO(q, true)).toList();
        }
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
}

