package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.Chapter;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
@Schema(description = "章节基础属性")
public class ChapterMeteInfoDTO implements Serializable {
    @Schema(description = "章节id",example = "xxxzzzz")
    String id;
    @Schema(description = "章节名称",example = "我是一个章节名称")
    String name;
    @Schema(description = "章节序号",example = "1")
    Integer chapterNumber;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public ChapterMeteInfoDTO(Chapter chapter) {
        this.id = chapter.getId();
        this.name = chapter.getName();
        this.chapterNumber = chapter.getChapterNumber();
    }
}
