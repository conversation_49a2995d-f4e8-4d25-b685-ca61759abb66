package com.unipus.digitalbook.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "成功与否")
public class BooleanDTO implements Serializable {
    private boolean value;

    public BooleanDTO(boolean value) {
        this.value = value;
    }

    public boolean getValue() {
        return value;
    }

    public void setValue(boolean value) {
        this.value = value;
    }
}
