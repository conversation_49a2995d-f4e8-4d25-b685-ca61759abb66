package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.dto.question.QuestionDTO;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.enums.QuestionRelationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

@Schema(description = "试卷大题组信息")
public class PaperBigQuestionGroupDTO extends BigQuestionGroupDTO {

    public PaperBigQuestionGroupDTO(){
        super();
    }

    public PaperBigQuestionGroupDTO(BigQuestionGroup bigQuestionGroup, boolean isReturnAnswer) {
        super(bigQuestionGroup, isReturnAnswer);
        List<Question> questions = bigQuestionGroup.getQuestions();
        if (CollectionUtils.isNotEmpty(questions)) {
            // 取得默认题与推荐题列表映射
            Map<String, List<Question>> recommendsMap = new HashMap<>();
            for (Question question : questions) {
                List<Question> recommendQuestions = getRecommendedQuestions(question, questions);
                if(CollectionUtils.isEmpty(recommendQuestions)){
                    continue;
                }
                recommendsMap.put(question.getBizQuestionId(), recommendQuestions);
            }

            // 如果存在推荐题，重新构建题目列表
            if(MapUtils.isNotEmpty(recommendsMap)) {
                // 清理推荐题列表
                List<String> recommendIds = recommendsMap.values().stream().flatMap(Collection::stream).map(Question::getBizQuestionId).toList();
                questions.removeIf(q -> recommendIds.contains(q.getBizQuestionId()));

                // 构建题目列表
                List<QuestionDTO> questionDTOS = questions.stream()
                        .map(q -> PaperQuestionDTO.build(q, recommendsMap.get(q.getBizQuestionId()), isReturnAnswer))
                        .toList();

                super.setList(questionDTOS);
            }
        }
    }

    private List<Question> getRecommendedQuestions(Question defaultQuestion, List<Question> questions) {
        QuestionText questionText = defaultQuestion.getQuestionText();
        if (CollectionUtils.isEmpty(questions) || questionText == null) {
            return null;
        }

        List<String> targetRelationIds = questionText.getTargetRelationIds(QuestionRelationTypeEnum.RECOMMEND);
        if(CollectionUtils.isEmpty(targetRelationIds)){
            return null;
        }

        return questions.stream().filter(q -> targetRelationIds.contains(q.getBizQuestionId())).toList();
    }
}
