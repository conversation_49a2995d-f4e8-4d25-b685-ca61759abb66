package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.question.QuestionDTO;
import com.unipus.digitalbook.model.entity.question.Question;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Schema(description = "题目信息")
public class PaperQuestionDTO extends QuestionDTO {

    @Schema(description = "推荐题列表")
    private List<QuestionDTO> recommends ;

    public PaperQuestionDTO() {
        super();
    }

    public PaperQuestionDTO(Question question, List<Question> recommends, boolean isReturnAnswer) {
        super(question, isReturnAnswer);
        if(CollectionUtils.isNotEmpty(recommends)) {
            this.recommends = recommends.stream().map(r -> new QuestionDTO(r, isReturnAnswer)).toList();
        }
    }

    public static QuestionDTO build(Question question, List<Question> recommends, boolean isReturnAnswer){
        return new PaperQuestionDTO(question, recommends, isReturnAnswer);
    }

    public List<QuestionDTO> getRecommends() {
        return recommends;
    }

    public void setRecommends(List<QuestionDTO> recommends) {
        this.recommends = recommends;
    }
}
