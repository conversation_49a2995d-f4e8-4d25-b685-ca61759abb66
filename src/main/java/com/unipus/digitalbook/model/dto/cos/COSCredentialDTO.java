package com.unipus.digitalbook.model.dto.cos;

import com.unipus.digitalbook.model.entity.cos.COSCredential;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * COS临时密钥凭证DTO
 *
 * <AUTHOR>
 * @date 2024/12/11 15:58
 */
@Data
@Schema(description = "COS临时密钥凭证")
public class COSCredentialDTO implements Serializable {

    @Schema(description = "过期时间字符串")
    private String expiration;

    @Schema(description = "开始时间戳")
    private long startTime;

    @Schema(description = "过期时间戳")
    private long expiredTime;

    @Schema(description = "临时访问密钥ID")
    private String tmpSecretId;

    @Schema(description = "临时访问密钥")
    private String tmpSecretKey;

    @Schema(description = "会话令牌")
    private String sessionToken;

    @Schema(description = "用户路径前缀")
    private String domainPathPrefix;

    /**
     * 通过实体构造DTO
     */
    public COSCredentialDTO(COSCredential entity) {
        if (entity == null) {
            return;
        }
        this.expiration = entity.getExpiration();
        this.startTime = entity.getStartTime();
        this.expiredTime = entity.getExpiredTime();
        this.tmpSecretId = entity.getTmpSecretId();
        this.tmpSecretKey = entity.getTmpSecretKey();
        this.sessionToken = entity.getSessionToken();
    }
}
