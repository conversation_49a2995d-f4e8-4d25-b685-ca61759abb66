package com.unipus.digitalbook.model.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "判题任务结果列表")
public class JudgeTaskListDTO implements Serializable {
    @Schema(description = "判题任务结果列表")
    private List<JudgeTaskDTO> judgeTaskList;

    public JudgeTaskListDTO() {
    }
    public JudgeTaskListDTO(List<JudgeTaskDTO> judgeTaskList) {
        this.judgeTaskList = judgeTaskList;
    }
    public List<JudgeTaskDTO> getJudgeTaskList() {
        return judgeTaskList;
    }

    public void setJudgeTaskList(List<JudgeTaskDTO> judgeTaskList) {
        this.judgeTaskList = judgeTaskList;
    }
}
