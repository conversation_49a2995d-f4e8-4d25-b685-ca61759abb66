package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "教材用户列表")
public class BookUserDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", example = "1")
    Long userId;

    @Schema(description = "用户名", example = "张三")
    String userName;

    @Schema(description = "手机号", example = "138****8000")
    String mobile;

    @Schema(description = "是否有效", example = "true")
    Boolean userValid = Boolean.TRUE;

    public BookUserDTO(ResourceUser resourceUser) {
        this.userId = resourceUser.getUserId();
        this.userName = resourceUser.getUserName();
        this.mobile = resourceUser.getMobile();
        this.userValid = resourceUser.getUserValid();
    }

    public BookUserDTO(UserInfo info) {
        if (info == null) {
            return;
        }
        this.userId = info.getId();
        this.userName = info.getName();
        this.mobile = UserUtil.desensitization(info.getCellPhone());
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Boolean getUserValid() {
        return userValid;
    }

    public void setUserValid(Boolean userValid) {
        this.userValid = userValid;
    }
}
