package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.tag.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "试卷标签列表DTO")
public class PaperTagListDTO implements Serializable {

    @Schema(description = "试卷标签列表")
    private List<TagDTO> paperTags;

    public PaperTagListDTO(List<Tag> paperTags) {
        this.paperTags = paperTags.stream().map(TagDTO::new).toList();
    }

    public List<TagDTO> getPaperTags() {
        return paperTags;
    }

    public void setPaperTags(List<TagDTO> paperTags) {
        this.paperTags = paperTags;
    }
}
