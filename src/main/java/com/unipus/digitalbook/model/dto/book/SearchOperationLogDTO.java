package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.SearchOperationLog;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "用户教材操作日志传输对象")
public class SearchOperationLogDTO implements Serializable {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "教材ID", example = "1")
    private String bookId;

    @Schema(description = "操作用户ID", example = "1")
    private Long operationUserId;

    @Schema(description = "操作用户名称", example = "张三")
    private String operationUserName;

    @Schema(description = "操作时间", example = "1630000000000")
    private Long operationTime;

    @Schema(description = "操作内容", example = "新增 “第三章 1”章节")
    private String operationContent;

    @Schema(description = "操作类型：1-新增 2-编辑 3-删除", example = "1")
    private Integer operationType;

    public SearchOperationLogDTO() {
    }

    public SearchOperationLogDTO(SearchOperationLog bookOperationLog) {
        if (bookOperationLog == null) {
            return;
        }
        this.id = bookOperationLog.getId();
        this.bookId = bookOperationLog.getBookId();
        this.operationUserId = bookOperationLog.getOperationUserId();
        this.operationUserName = bookOperationLog.getOperationUserName();
        this.operationTime = bookOperationLog.getOperationTime() != null ? bookOperationLog.getOperationTime().getTime() : null;
        this.operationContent = bookOperationLog.getOperationContent();
        this.operationType = bookOperationLog.getOperationType();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Long getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(Long operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Long operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    @Override
    public String toString() {
        return "BookOperationLogDTO{" +
                "id=" + id +
                ", bookId=" + bookId +
                ", operationUserId=" + operationUserId +
                ", operationUserName='" + operationUserName + '\'' +
                ", operationTime=" + operationTime +
                ", operationContent='" + operationContent + '\'' +
                ", operationType=" + operationType +
                '}';
    }
}