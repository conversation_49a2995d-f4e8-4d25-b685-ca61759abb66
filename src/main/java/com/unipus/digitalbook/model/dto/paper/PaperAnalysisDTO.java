package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.UserPaperAnswer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 试卷作答解析DTO
 */
@Schema(description = "试卷作答解析DTO")
public class PaperAnalysisDTO implements Serializable {
    @Schema(description = "试卷实例DTO")
    private PaperInstanceDTO paperInstanceDTO;

    @Schema(description = "用户作答记录DTO")
    private UserPaperAnswerDTO userPaperAnswerDTO;

    public PaperAnalysisDTO(PaperInstance paperInstance, UserPaperAnswer userPaperAnswer) {
        this.paperInstanceDTO = new PaperInstanceDTO(paperInstance, true);
        this.userPaperAnswerDTO = UserPaperAnswerDTO.build(userPaperAnswer);
    }

    public PaperInstanceDTO getPaperInstanceDTO() {
        return paperInstanceDTO;
    }

    public void setPaperInstanceDTO(PaperInstanceDTO paperInstanceDTO) {
        this.paperInstanceDTO = paperInstanceDTO;
    }

    public UserPaperAnswerDTO getUserPaperAnswerDTO() {
        return userPaperAnswerDTO;
    }

    public void setUserPaperAnswerDTO(UserPaperAnswerDTO userPaperAnswerDTO) {
        this.userPaperAnswerDTO = userPaperAnswerDTO;
    }
}
