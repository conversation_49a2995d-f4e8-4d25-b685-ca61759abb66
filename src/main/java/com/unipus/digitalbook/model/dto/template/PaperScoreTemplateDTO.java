package com.unipus.digitalbook.model.dto.template;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateStatusEnum;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.util.*;

@Data
@Schema(description = "模板信息")
public class PaperScoreTemplateDTO implements Serializable {

    @Schema(description = "模板Id")
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "模板类型，1：挑战评价，2：诊断评价")
    private Integer type;

    /**
     * {@link PaperScoreTemplateStatusEnum}
     */
    @Schema(description = "模板状态 0：未发布，1：已发布")
    private Integer status;

    @Schema(description = "创建用户名称")
    private String createUserName;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "模板详情")
    private List<PaperScoreTemplateDetailDTO> templateDetailList;

    public static PaperScoreTemplateDTO assemblyPaperScoreTemplateDTO(PaperScoreTemplate paperScoreTemplate, UserService userService){
        if (ObjectUtils.isEmpty(paperScoreTemplate)){
            return null;
        }
        PaperScoreTemplateDTO paperScoreTemplateDTO = new PaperScoreTemplateDTO();
        paperScoreTemplateDTO.setId(paperScoreTemplate.getId());
        paperScoreTemplateDTO.setName(paperScoreTemplate.getName());
        paperScoreTemplateDTO.setType(paperScoreTemplate.getType());
        paperScoreTemplateDTO.setStatus(paperScoreTemplate.getStatus());
        paperScoreTemplateDTO.setUpdateTime(paperScoreTemplate.getUpdateTime());
        if (ObjectUtils.isNotEmpty(userService)){
            Map<Long, UserInfo> userMap = userService.getUserMap(Collections.singletonList(paperScoreTemplate.getCreateUserId()));
            if (MapUtils.isNotEmpty(userMap)){
                UserInfo userInfo = userMap.get(paperScoreTemplate.getCreateUserId());
                paperScoreTemplateDTO.setCreateUserName(userInfo.getName());
            }
        }
        if (CollectionUtils.isNotEmpty(paperScoreTemplate.getTemplateDetailList())){
            paperScoreTemplateDTO.setTemplateDetailList(paperScoreTemplate.getTemplateDetailList().stream().map(PaperScoreTemplateDetailDTO::assemblyPaperScoreTemplateDetailDTO).toList());
        }
        return paperScoreTemplateDTO;
    }
}
