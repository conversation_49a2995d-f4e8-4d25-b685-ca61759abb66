package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@Schema(description = "教材简要信息列表")
public class BookSimpleDataListDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "教材列表")
    private List<BookSimpleDataDTO> books;

}
