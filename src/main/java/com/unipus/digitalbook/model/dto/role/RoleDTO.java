package com.unipus.digitalbook.model.dto.role;

import com.unipus.digitalbook.model.entity.role.Role;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "角色模型")
public class RoleDTO implements Serializable {
    @Schema(description = "角色ID")
    private Long id;
    @Schema(description = "角色名称")
    private String name;
    @Schema(description = "角色描述")
    private String desc;
    @Schema(description = "角色状态 1启用 0禁止")
    private Integer status;
    @Schema(description = "创建时间")
    private Long createTime;
    @Schema(description = "更新时间")
    private Long updateTime;

    public static Role toRole(RoleDTO roleDTO) {
        Role role = new Role();
        role.setId(roleDTO.getId());
        role.setName(roleDTO.getName());
        role.setDesc(roleDTO.getDesc());
        role.setStatus(roleDTO.getStatus());
        return role;
    }
    public static RoleDTO toRoleDTO(Role role) {
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(role.getId());
        roleDTO.setName(role.getName());
        roleDTO.setDesc(role.getDesc());
        roleDTO.setStatus(role.getStatus());
        roleDTO.setCreateTime(role.getCreateTime() != null ? role.getCreateTime().getTime() : null);
        roleDTO.setUpdateTime(role.getUpdateTime() != null ? role.getUpdateTime().getTime() : null);
        return roleDTO;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
