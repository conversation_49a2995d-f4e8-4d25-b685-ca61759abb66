package com.unipus.digitalbook.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "API数据")
public class ApiDTO implements Serializable {
    @Schema(description = "所属controller")
    String controller;
    @Schema(description = "controller描述")
    String controllerDesc;
    @Schema(description = "方法名")
    String methodName;
    @Schema(description = "资源说明")
    String apiDesc;
    @Schema(description = "资源名")
    String apiSummary;
    @Schema(description = "资源地址")
    String apiUrl;

    @Schema(description = "请求方式",examples = {"POST","GET","DELETE","PUT"})
    String requestType;
    @Schema(description = "是否已分配")
    Boolean assigned = Boolean.FALSE;

    @Schema(description = "分配时间")
    Long assignedTime;

    public ApiDTO() {
    }
    public ApiDTO(AuthPermissionDTO authPermissionDTO, ControllerPermissionDTO controllerPermission) {
        this.methodName = authPermissionDTO.getMethodName();
        this.apiDesc = authPermissionDTO.apiDesc;
        this.apiSummary = authPermissionDTO.getApiSummary();
        this.apiUrl = authPermissionDTO.getApiUrl();
        this.requestType = authPermissionDTO.getRequestType();
        this.assigned = authPermissionDTO.getAssigned();
        this.assignedTime = authPermissionDTO.getAssignedTime();
        this.controller = controllerPermission.getController();
        this.controllerDesc = controllerPermission.getControllerDesc();
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getApiDesc() {
        return apiDesc;
    }

    public void setApiDesc(String apiDesc) {
        this.apiDesc = apiDesc;
    }

    public String getApiSummary() {
        return apiSummary;
    }

    public void setApiSummary(String apiSummary) {
        this.apiSummary = apiSummary;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public Boolean getAssigned() {
        return assigned;
    }

    public void setAssigned(Boolean assigned) {
        this.assigned = assigned;
    }

    public Long getAssignedTime() {
        return assignedTime;
    }

    public void setAssignedTime(Long assignedTime) {
        this.assignedTime = assignedTime;
    }

    public String getController() {
        return controller;
    }

    public void setController(String controller) {
        this.controller = controller;
    }

    public String getControllerDesc() {
        return controllerDesc;
    }

    public void setControllerDesc(String controllerDesc) {
        this.controllerDesc = controllerDesc;
    }
}
