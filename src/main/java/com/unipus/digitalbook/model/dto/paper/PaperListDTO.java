package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.Paper;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "试卷列表DTO")
public class PaperListDTO implements Serializable {

    @Schema(description = "试卷列表")
    private List<PaperDTO> paperDTOS;

    public PaperListDTO(List<Paper> papers) {
        this.paperDTOS = papers.stream().map(paper->new PaperDTO(paper, null)).toList();
    }

    public List<PaperDTO> getPaperDTOS() {
        return paperDTOS;
    }

    public void setPaperDTOS(List<PaperDTO> paperDTOS) {
        this.paperDTOS = paperDTOS;
    }
}
