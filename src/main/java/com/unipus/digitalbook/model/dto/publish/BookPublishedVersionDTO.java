package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.publish.BookPublishedVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;


@Schema(description = "教材已发布版本实体类")
public class BookPublishedVersionDTO implements Serializable {

    @Schema(description = "发布版本 ID")
    private Long id;

    @Schema(description = "教材 ID")
    private String bookId;

    @Schema(description = "版本号")
    private String versionNum;

    @Schema(description = "格式版本号")
    private String formatVersionNum;

    @Schema(description = "上架时间")
    private Long publishTime;

    @Schema(description = "创建者ID")
    private Long createBy;

    @Schema(description = "创建者")
    private String creatorName;

    public BookPublishedVersionDTO(BookPublishedVersion bookPublishedVersion) {
        this.id = bookPublishedVersion.getId();
        this.bookId = bookPublishedVersion.getBookId();
        this.versionNum = bookPublishedVersion.getVersionNum();
        this.formatVersionNum = bookPublishedVersion.getFormatVersionNum();
        this.publishTime = bookPublishedVersion.getPublishTime() != null ? bookPublishedVersion.getPublishTime().getTime() : null;
        this.createBy = bookPublishedVersion.getCreateBy();
        this.creatorName = bookPublishedVersion.getCreatorName();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public String getFormatVersionNum() {
        return formatVersionNum;
    }

    public void setFormatVersionNum(String formatVersionNum) {
        this.formatVersionNum = formatVersionNum;
    }

    public Long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Long publishTime) {
        this.publishTime = publishTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
}
