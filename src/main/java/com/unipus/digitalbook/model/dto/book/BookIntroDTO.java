package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookIntro;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "教材简介 DTO")
public class BookIntroDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "当前实体的ID，与versionId一致")
    private Long id;

    @Schema(description = "关联教材ID", example = "B001")
    private String bookId;

    @Schema(description = "教材详细介绍", example = "这是一本介绍计算机科学基础的教材，适合初学者学习。")
    private String description;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(
            title = "创建时间",
            description = "记录创建的时间"
    )
    private Long createTime;

    @Schema(
            title = "最后更新时间",
            description = "记录最后更新的时间"
    )
    private Long updateTime;

    @Schema(
            title = "创建者ID",
            description = "创建该记录的用户ID",
            example = "1001"
    )
    private Long createBy;

    @Schema(
            title = "最后更新者ID",
            description = "最后更新该记录的用户ID",
            example = "1002"
    )
    private Long updateBy;

    public BookIntroDTO(BookIntro bookIntro) {
        this.id = bookIntro.getId();
        this.bookId = bookIntro.getBookId();
        this.description = bookIntro.getDescription();
        this.versionNumber = bookIntro.getVersionNumber();
        this.createTime = bookIntro.getCreateTime().getTime();
        this.updateTime = bookIntro.getUpdateTime().getTime();
        this.createBy = bookIntro.getCreateBy();
        this.updateBy = bookIntro.getUpdateBy();
    }

}
