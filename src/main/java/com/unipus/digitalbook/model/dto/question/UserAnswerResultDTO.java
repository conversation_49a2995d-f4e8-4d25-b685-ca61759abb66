package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "用户答题结果")
public class UserAnswerResultDTO implements Serializable {

    @Schema(description = "子题目ID")
    private String childId;

    @Schema(description = "答题业务id")
    private String bizAnswerId;

    @Schema(description = "用户答案")
    private String userAnswerValue;

    @Schema(description = "用户得分")
    private BigDecimal score;

    @Schema(description = "评测结果, 不同题型返回评测结果结构不同")
    private String evaluation;

    @Schema(description = "大题题状态 1错误 2正确 3半对")
    private Integer status;

    public UserAnswerResultDTO() {
    }

    public UserAnswerResultDTO(UserAnswer userAnswer) {
        this.setChildId(userAnswer.getBizQuestionId());
        this.setScore(ScoreUtil.keepTwoDecimal(userAnswer.getScore()));
        this.setEvaluation(userAnswer.getEvaluation());
        this.setUserAnswerValue(userAnswer.getAnswer());
        this.setBizAnswerId(userAnswer.getBizAnswerId());
        this.setStatus(userAnswer.getStatus());
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public String getUserAnswerValue() {
        return userAnswerValue;
    }

    public void setUserAnswerValue(String userAnswerValue) {
        this.userAnswerValue = userAnswerValue;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation;
    }

    public String getBizAnswerId() {
        return bizAnswerId;
    }

    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }
}
