package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.entity.user.SearchUserList;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "用户列表模型")
public class SearchUserListDTO implements Serializable {

    @Schema(description = "用户列表")
    private List<SearchUserDTO> userList;

    @Schema(description = "总数量")
    private Integer total;

    public SearchUserListDTO() {
    }

    public SearchUserListDTO(SearchUserList searchUserList) {
        if (searchUserList == null) {
            return;
        }
        this.userList = searchUserList.getUserList().stream()
                .map(SearchUserDTO::new)
                .toList();
        this.total = searchUserList.getTotal();
    }

    public List<SearchUserDTO> getUserList() {
        return userList;
    }

    public void setUserList(List<SearchUserDTO> userList) {
        this.userList = userList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
