package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.common.PageParams;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.StringJoiner;

@Schema(description = "管理员搜索参数")
public class SearchUserParam implements Serializable {

    @Schema(description = "手机号码", example = "13800138000")
    private String cellPhone;

    @Schema(description = "组织ID", example = "12345")
    private Long orgId;

    @Schema(description = "激活状态", example = "1")
    private Integer status;

    @Schema(description = "用户名称", example = "张三")
    private String userName;

    @Schema(description = "分页与排序")
    private PageParams pageParams;

    public String getCellPhone() {
        return cellPhone;
    }

    public SearchUserParam setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
        return this;
    }

    public Long getOrgId() {
        return orgId;
    }

    public SearchUserParam setOrgId(Long orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public SearchUserParam setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getUserName() {
        return userName;
    }

    public SearchUserParam setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public SearchUserParam setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", SearchUserParam.class.getSimpleName() + "[", "]")
                .add("cellPhone='" + cellPhone + "'")
                .add("orgId=" + orgId)
                .add("status=" + status)
                .add("userName=" + userName)
                .toString();
    }
}

