package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "编辑角色请求参数")
public class EditRoleParam implements Params {
    @Schema(description = "角色Id")
    private Long id;
    @Schema(description = "角色描述")
    private String desc;
    @Schema(description = "角色名称")
    private String name;

    public Role toRole(Long userId) {
        Role role = new Role();
        role.setId(id);
        role.setDesc(desc);
        role.setName(name);
        role.setCreateBy(userId);
        role.setUpdateBy(userId);
        return role;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public void valid() {
        if (this.getId() == null || this.getId() <= 0) {
            throw new IllegalArgumentException("角色id不能为空");
        }
        if (StringUtils.isBlank(this.getName())) {
            throw new IllegalArgumentException("请输入角色名称");
        }
        if (this.getName().length() > 10) {
            throw new IllegalArgumentException("角色名字长度不能超过10个字符");
        }
        if (this.getDesc().length() > 50) {
            throw new IllegalArgumentException("角色说明长度不能超过50个字符");
        }
    }
}
