package com.unipus.digitalbook.model.params.assistant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "教材数字人开关参数")
public class AssistantBookSwitchParam implements Serializable {

    @Schema(description = "教材ids")
    @NotEmpty
    private List<String> bookIds;
}
