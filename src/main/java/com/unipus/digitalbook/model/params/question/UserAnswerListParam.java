package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "用户答题列表参数")
public class UserAnswerListParam {
    @Schema(description = "用户答题列表")
    private List<UserAnswerParam> userAnswers;

    @Schema(description = "题目版本号")
    private String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;

    public UserAnswerList toEntity(String ssoId) {
        UserAnswerList userAnswerList = new UserAnswerList();
        userAnswerList.setUserAnswers(this.userAnswers.stream()
                .map(u -> u.toEntity(ssoId, versionNumber, null)).toList());
        return userAnswerList;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public List<UserAnswerParam> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswerParam> userAnswers) {
        this.userAnswers = userAnswers;
    }
}
