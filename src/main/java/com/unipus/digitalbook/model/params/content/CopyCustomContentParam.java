package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Schema(description = "复制自建内容参数")
public class CopyCustomContentParam implements Params {

    @Schema(description = "自建内容复制项列表")
    private List<CopyContentItemParam> itemList;

    @Schema(description = "内容状态（0：编写中/1：待发布/2：已发布）", required = true)
    private Integer status;

    public List<CopyContentItemParam> getItemList() {
        return itemList;
    }

    public void setItemList(List<CopyContentItemParam> itemList) {
        this.itemList = itemList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(itemList)) {
            throw new IllegalArgumentException("自建内容复制项列表不能为空");
        }
        // 校验复制内容列表中的每一项
        for (CopyContentItemParam item : itemList) {
            item.valid();
        }
        if (status == null) {
            throw new IllegalArgumentException("内容状态不能为空");
        }
        if (status < 0 || status > 2) {
            throw new IllegalArgumentException("内容状态值无效，必须为0（编写中）、1（待发布）或2（已发布）");
        }
    }
}
