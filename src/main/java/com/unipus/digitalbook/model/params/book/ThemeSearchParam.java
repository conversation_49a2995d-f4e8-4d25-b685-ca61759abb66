package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.Theme;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * 教材主题实体
 */
@Schema(description = "教材主题实体")
public class ThemeSearchParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主题ID")
    private Long id;

    @Schema(description = "主题名称")
    private String name;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Theme toEntity() {
        Theme theme = new Theme();
        theme.setId(id);
        theme.setName(name);
        return theme;
    }
}
