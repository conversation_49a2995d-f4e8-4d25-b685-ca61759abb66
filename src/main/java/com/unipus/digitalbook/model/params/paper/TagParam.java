package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Schema(description = "标签参数")
public class TagParam implements Params {
    @Schema(description = "标签类型:1：技能点")
    private Integer tagType;
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "下级标签列表")
    private List<TagParam> children;

    @Override
    public void valid() {
        if(tagType == null){
            throw new IllegalArgumentException("标签类型不能为空");
        }
        if(!StringUtils.hasText(tagName)){
            throw new IllegalArgumentException("标签名称不能为空");
        }
        if(CollectionUtils.isNotEmpty(children)){
            children.forEach(TagParam::valid);
        }
    }

    public Tag toEntity() {
        Tag tag = new Tag();
        tag.setTagType(tagType);
        tag.setTagName(tagName);
        if(CollectionUtils.isNotEmpty(children)){
            tag.setChildren(children.stream().map(TagParam::toEntity).toList());
        }
        return tag;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public List<TagParam> getChildren() {
        return children;
    }

    public void setChildren(List<TagParam> children) {
        this.children = children;
    }

}
