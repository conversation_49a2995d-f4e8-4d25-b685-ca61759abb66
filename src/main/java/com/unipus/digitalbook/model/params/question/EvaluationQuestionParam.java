package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.EvaluationQuestion;

/**
 * 量表题参数
 */
public class EvaluationQuestionParam extends QuestionBaseParam {


    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        EvaluationQuestion evaluationQuestion = new EvaluationQuestion();
        evaluationQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return evaluationQuestion;
    }
}
