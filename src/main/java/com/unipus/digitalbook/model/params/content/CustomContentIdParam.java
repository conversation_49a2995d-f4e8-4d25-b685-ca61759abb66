package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Schema(description = "自建内容信息参数")
public class CustomContentIdParam implements Params {

    @Schema(description = "自建内容业务id列表")
    private List<String> bizIds;

    public List<String> getBizIds() {
        return bizIds;
    }

    public void setBizIds(List<String> bizIds) {
        this.bizIds = bizIds;
    }

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(bizIds)) {
            throw new IllegalArgumentException("自建内容业务id列表不能为空");
        }
    }
}
