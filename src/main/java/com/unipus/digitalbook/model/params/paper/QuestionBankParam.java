package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "题库参数")
public class QuestionBankParam implements Params {
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "题库ID (UUID)")
    private String bankId;
    @Schema(description = "题库名称")
    private String bankName;
    @Schema(description = "题库每题分数")
    private Integer questionScore;
    @Schema(description = "每轮随机抽取题目数")
    private Integer questionsPerRound;
    @Schema(description = "题库内容")
    private String content;

    @Override
    public void valid() {
        if (!StringUtils.hasText(paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if (!StringUtils.hasText(bankId)) {
            throw new IllegalArgumentException("题库ID不能为空");
        }
        if (!StringUtils.hasText(bankName)) {
            throw new IllegalArgumentException("题库名称不能为空");
        }
        if (questionScore == null) {
            throw new IllegalArgumentException("题库每题分数不能为空");
        }
        if (questionsPerRound == null) {
            throw new IllegalArgumentException("每轮随机抽取题目数不能为空");
        }
    }

    public QuestionBank toEntity() {
        QuestionBank questionBank = new QuestionBank();
        questionBank.setPaperId(this.paperId);
        questionBank.setBankId(this.bankId);
        questionBank.setBankName(this.bankName);
        questionBank.setQuestionsPerRound(this.questionsPerRound);
        questionBank.setQuestionScore(this.questionScore);
        questionBank.setContent(this.content);
        questionBank.setVersionNumber(IdentifierUtil.DEFAULT_VERSION_NUMBER);
        return questionBank;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Integer questionScore) {
        this.questionScore = questionScore;
    }

    public Integer getQuestionsPerRound() {
        return questionsPerRound;
    }

    public void setQuestionsPerRound(Integer questionsPerRound) {
        this.questionsPerRound = questionsPerRound;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
