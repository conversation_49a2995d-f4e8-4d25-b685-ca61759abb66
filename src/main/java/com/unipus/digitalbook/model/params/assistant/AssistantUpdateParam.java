package com.unipus.digitalbook.model.params.assistant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "教材修改数字人参数")
public class AssistantUpdateParam implements Serializable {

    @Schema(description = "助手id")
    @NotBlank
    private String id;

    @Schema(description = "助手名称")
    private String name;

    @Schema(description = "配置类型")
    @NotNull
    private Integer configType;

    @Schema(description = "助教类型")
    @NotNull
    private Integer type;

    @Schema(description = "模板id")
    private String templateId;

    @Schema(description = "模板json")
    private String templateJson;

}
