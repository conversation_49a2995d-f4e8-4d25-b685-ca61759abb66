package com.unipus.digitalbook.model.params.template;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.model.enums.StatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "模板搜索参数")
public class PaperScoreTemplateSearchParam implements Serializable {

    @Schema(description = "模板名称")
    private String name;

    /**
     * {@link PaperScoreTemplateTypeEnum}
     */
    @Schema(description = "模板类型 1：挑战评价，2：诊断评价")
    private Integer type;

    /**
     * {@link StatusEnum}
     */
    @Schema(description = "模板状态")
    private Integer status;

    @Schema(description = "分页参数")
    private PageParams pageParams;
}
