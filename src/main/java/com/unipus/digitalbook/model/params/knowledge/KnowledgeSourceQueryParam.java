package com.unipus.digitalbook.model.params.knowledge;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO;
import lombok.Data;
import org.apache.poi.util.StringUtil;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceQueryParam implements Params {

    private Long id;

    /**
     * 教材Id
     */
    @JsonAlias(value = "courseIdStr")
    private String bookId;

    /**
     * 单元Id
     */
    @JsonAlias(value = "unitId")
    private String chapterId;

    /**
     * 资源类型 1 文字段落 2 视频 3 题目 4 音频
     */
    private Integer type;

    /**
     * 课程图谱Id
     */
    private Long courseKnowledgeId;

    /**
     * 多媒体文件key
     */
    private String multimediaKey;

    /**
     * 三方图谱Id
     */
    private String knowledgeId;

    /**
     * 三方资源Id
     */
    private String sourceId;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        // 教材资源信息验证
        if (StringUtil.isBlank(this.getBookId())) {
            result.addError("教材IdStr为空", "请输入教材IdStr");
        }

        if (StringUtil.isBlank(this.getChapterId())) {
            result.addError("教材单元名称为空", "请输入教材单元");
        }
        if (null == this.getCourseKnowledgeId()) {
            result.addError("教材知识图谱关系Id为空", "请输入教材知识图谱关系Id为空");
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }

    public BookKnowledgeResourceCompleteInfoPO toQueryPO() {
        BookKnowledgeResourceCompleteInfoPO queryPO = new BookKnowledgeResourceCompleteInfoPO();
        queryPO.setBookId(this.getBookId());
        queryPO.setChapterId(this.getChapterId());
        queryPO.setType(this.getType());
        queryPO.setBookKnowledgeId(this.getCourseKnowledgeId());
        queryPO.setMultimediaKey(this.getMultimediaKey());
        queryPO.setKnowledgeId(this.getKnowledgeId());
        queryPO.setKnowledgeSourceId(this.getSourceId());
        return queryPO;
    }


}
