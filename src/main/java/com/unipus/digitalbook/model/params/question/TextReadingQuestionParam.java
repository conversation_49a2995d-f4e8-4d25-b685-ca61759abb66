package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TextReadingQuestion;
import org.springframework.util.CollectionUtils;

/**
 * 文本跟读题参数
 */
public class TextReadingQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        TextReadingQuestion textReadingQuestion = new TextReadingQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        if (!CollectionUtils.isEmpty(getRelevancyList())) {
            currentQuestionText.setRelevancy(getRelevancyList().stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        textReadingQuestion.setQuestionText(currentQuestionText);
        return textReadingQuestion;
    }
}
