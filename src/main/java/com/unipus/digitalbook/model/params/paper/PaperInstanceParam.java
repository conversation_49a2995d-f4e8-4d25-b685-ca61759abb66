package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "试卷实例参数")
public class PaperInstanceParam implements Params {
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "诊断卷的测试模式 1:诊断模式/2:推荐模式。(非诊断卷可为空值)", nullable = true)
    private Integer testMode = 1;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if (!StringUtils.hasText(this.versionNumber)) {
            throw new IllegalArgumentException("试卷版本号不能为空");
        }
    }

    public PaperInstance toEntity(UserAccessInfo userAccessInfo) {
        PaperInstance  paperInstance = new PaperInstance();
        paperInstance.setPaperId(this.paperId);
        paperInstance.setVersionNumber(this.versionNumber);
        paperInstance.setTestMode(UnitTestModeEnum.of(this.testMode));
        paperInstance.setOpenId(userAccessInfo.getOpenId());
        paperInstance.setTenantId(userAccessInfo.getTenantId());
        return paperInstance;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getTestMode() {
        return testMode;
    }

    public void setTestMode(Integer testMode) {
        this.testMode = testMode;
    }

}
