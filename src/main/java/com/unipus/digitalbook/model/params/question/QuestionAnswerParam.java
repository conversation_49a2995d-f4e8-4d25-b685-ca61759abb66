package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import io.swagger.v3.oas.annotations.media.Schema;

public class QuestionAnswerParam {
    @Schema(description = "答案 ID")
    private String answerId;
    @Schema(description = "答案的文本内容")
    private String answerValue;

    public QuestionAnswer toEntity(Long userId) {
        QuestionAnswer questionAnswer = new QuestionAnswer();
        questionAnswer.setCorrectAnswerText(this.getAnswerValue());
        questionAnswer.setCorrectAnswerId(this.getAnswerId());
        questionAnswer.setCreateBy(userId);
        questionAnswer.setUpdateBy(userId);
        questionAnswer.setEnable(true);
        return questionAnswer;
    }

    public String getAnswerValue() {
        return answerValue;
    }

    public void setAnswerValue(String answerValue) {
        this.answerValue = answerValue;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }
}
