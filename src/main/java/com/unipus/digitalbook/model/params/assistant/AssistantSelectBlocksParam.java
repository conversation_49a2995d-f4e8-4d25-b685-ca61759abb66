package com.unipus.digitalbook.model.params.assistant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "教材数字人点选内容块参数")
public class AssistantSelectBlocksParam implements Serializable {

    @Schema(description = "教材id")
    @NotBlank
    private String bookId;

    @Schema(description = "章节id")
    @NotBlank
    private String chapterId;

    @Schema(description = "内容块ids")
    @NotEmpty
    private List<String> blockIds;
}
