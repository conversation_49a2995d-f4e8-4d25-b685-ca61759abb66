package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.paper.QuestionTagParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.IntStream;

@Schema(description = "题目列表参数")
@Data
public class PaperQuestionListParam implements Params {
    @Schema(description = "所属题组ID（试卷ID/题库ID）")
    private String parentId;

    @Schema(description = "大题组")
    private List<PaperBigQuestionGroupParam> bigQuestionGroupParams;

    @Schema(description = "标签列表")
    private List<QuestionTagParam> questionTagParams;

    @Override
    public void valid() {
        if (!StringUtils.hasText(parentId)) {
            throw new IllegalArgumentException("所属题组ID（试卷ID/题库ID）不能为空");
        }
        if(CollectionUtils.isNotEmpty(bigQuestionGroupParams)) {
            bigQuestionGroupParams.forEach(PaperBigQuestionGroupParam::valid);
        }
        if(CollectionUtils.isNotEmpty(questionTagParams)) {
            questionTagParams.forEach(QuestionTagParam::valid);
        }
    }

    /**
     * 转换为题组实体对象
     */
    public List<BigQuestionGroup> toBigQuestionGroupEntity(Long userId, Long parentId, String versionNumber) {
        if(CollectionUtils.isEmpty(bigQuestionGroupParams)){
            return List.of();
        }
        int size = bigQuestionGroupParams.size();
        return IntStream.range(0, size)
                .mapToObj(i -> {
                    PaperBigQuestionGroupParam param = bigQuestionGroupParams.get(i);
                    BigQuestionGroup entity = param.toEntity(userId, versionNumber);
                    entity.setParentId(parentId);
                    entity.setSortOrder(i);
                    return entity;
                }).toList();
    }

    /**
     * 转换为标签实体对象
     */
    public List<QuestionTag> toQuestionTagEntity() {
        if(CollectionUtils.isEmpty(questionTagParams)){
            return List.of();
        }
        return questionTagParams.stream().map(QuestionTagParam::toEntity).toList();
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<PaperBigQuestionGroupParam> getBigQuestionGroupParams() {
        return bigQuestionGroupParams;
    }

    public void setBigQuestionGroupParams(List<PaperBigQuestionGroupParam> bigQuestionGroupParams) {
        this.bigQuestionGroupParams = bigQuestionGroupParams;
    }

    public List<QuestionTagParam> getQuestionTagParams() {
        return questionTagParams;
    }

    public void setQuestionTagParams(List<QuestionTagParam> questionTagParams) {
        this.questionTagParams = questionTagParams;
    }
}
