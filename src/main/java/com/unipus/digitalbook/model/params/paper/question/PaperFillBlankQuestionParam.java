package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.FillBlankQuestion;

/**
 * 填空题参数
 */
public class PaperFillBlankQuestionParam extends PaperQuestionBaseParam {

    @Override
    public void valid() {

    }
    @Override
    protected Question toQuestion(QuestionText questionText) {
        FillBlankQuestion fillBlankQuestion = new FillBlankQuestion();
        fillBlankQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return fillBlankQuestion;
    }
}
