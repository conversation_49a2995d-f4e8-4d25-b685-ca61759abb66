package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.RolePermission;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.stream.Collectors;

@Schema(description = "角色分配接口权限请求参数")
public class AssignInterfaceParam implements Params {
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "菜单参数")
    private List<InterfacePermissionParam> interfacePermissions;

    public RolePermission toEntity() {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionItems(interfacePermissions.stream().map(InterfacePermissionParam::toEntity).collect(Collectors.toList()));
        return rolePermission;
    }
    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<InterfacePermissionParam> getInterfacePermissions() {
        return interfacePermissions;
    }

    public void setInterfacePermissions(List<InterfacePermissionParam> interfacePermissions) {
        this.interfacePermissions = interfacePermissions;
    }

    @Override
    public void valid() {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (interfacePermissions == null || interfacePermissions.isEmpty()) {
            throw new IllegalArgumentException("请选择至少一条数据");
        }
    }
}
