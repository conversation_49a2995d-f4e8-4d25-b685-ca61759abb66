package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.RolePermissionItem;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "菜单权限参数")
public class MenuPermissionParam implements Params {
    @Schema(description = "菜单id")
    private Long menuId;

    @Schema(description = "true分配，false取消分配")
    private boolean assigned;

    public RolePermissionItem toEntity() {
        RolePermissionItem rolePermissionItem = new RolePermissionItem();
        rolePermissionItem.setPermissionReference(String.valueOf(menuId));
        rolePermissionItem.setEnable(assigned);
        return rolePermissionItem;
    }
    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public boolean isAssigned() {
        return assigned;
    }

    public void setAssigned(boolean assigned) {
        this.assigned = assigned;
    }

    @Override
    public void valid() {
        if (menuId == null) {
            throw new IllegalArgumentException("菜单ID不能为空");
        }
    }
}
