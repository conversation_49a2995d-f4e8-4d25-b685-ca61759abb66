package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "试卷引用基础参数")
public class PaperReferenceSimpleParam implements Params {
    @Schema(description = "试卷在章节里面的插入位置")
    private String position;
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Override
    public void valid() {
        if (!StringUtils.hasText(this.position)) {
            throw new IllegalArgumentException("试卷在章节里面的插入位置不能为空");
        }
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
    }

    public PaperReference toEntity() {
        PaperReference paperReference = new PaperReference();
        paperReference.setPosition(this.position);
        paperReference.setPaperId(this.paperId);
        return paperReference;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

}
