package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "权限查询参数")
public class PermissionSearchParam implements Params {

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "资源类型：1：教材，2：章节")
    private Integer resourceType;

    @Schema(description = "用户Id")
    private Long userId;

    @Schema(description = "权限类型枚举：0/无，1/共享，2/阅读，4/编辑，8/拥有")
    private Integer permissionTypeCode;

    public PermissionSearchParam() {
    }

    public PermissionSearchParam(Long userId, ResourceTypeEnum resourceType, PermissionTypeEnum permissionType) {
        this.userId = userId;
        this.resourceType = resourceType.getCode();
        if (permissionType != null) {
            this.permissionTypeCode = permissionType.getCode();
        }
    }

    public PermissionSearchParam(String resourceId, ResourceTypeEnum resourceType, PermissionTypeEnum permissionType) {
        this.resourceId = resourceId;
        this.resourceType = resourceType.getCode();
        if (permissionType != null) {
            this.permissionTypeCode = permissionType.getCode();
        }
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getPermissionTypeCode() {
        return permissionTypeCode;
    }

    public void setPermissionTypeCode(Integer permissionTypeCode) {
        this.permissionTypeCode = permissionTypeCode;
    }

    @Override
    public void valid() {
        if (resourceId == null && userId==null) {
            throw new IllegalArgumentException("资源ID与用户ID不能同时为空");
        }
        if (PermissionTypeEnum.getPermissionTypeEnum(permissionTypeCode) == null) {
            throw new IllegalArgumentException("权限类型不能正确");
        }
    }

}
