package com.unipus.digitalbook.model.params.publish;

import com.unipus.digitalbook.model.entity.publish.BookPublishOrder;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 教材发布信息发布提交参数列表
 */
@Schema(description = "教材发布信息参数列表")
public class BookPublishParam implements Params {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "要发布的教材内容列表，包含基本信息，简介，版权信息，章节单元，配套资源，试卷")
    private List<PublishResourceParam> publishResourceParams;

    public List<PublishResourceParam> getBookCheckParams() {
        return publishResourceParams;
    }

    public void setBookCheckParams(List<PublishResourceParam> publishResourceParams) {
        this.publishResourceParams = publishResourceParams;
    }

    public BookPublishOrder toEntity(){
        BookPublishOrder bookPublishOrder = new BookPublishOrder();
        bookPublishOrder.setBookId(bookId);
        bookPublishOrder.setPublishResourceList(publishResourceParams.stream().map(PublishResourceParam::toEntity).toList());
        return bookPublishOrder;
    }

    @Override
    public void valid() {
        if(bookId == null){
            throw new IllegalArgumentException("bookId不能为空");
        }
        if(CollectionUtils.isEmpty(publishResourceParams)){
            throw new IllegalArgumentException("入参不能为空");
        }
        for (PublishResourceParam publishResourceParam : publishResourceParams) {
            publishResourceParam.valid();
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
}
