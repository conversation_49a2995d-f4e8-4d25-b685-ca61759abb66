package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "教材操作日志搜索参数")
public class SearchOperationLogParam implements Params {

    @Schema(description = "教材ID", example = "12345")
    private String bookId;

    @Schema(description = "用户名称", example = "张三")
    private String userName;

    @Schema(description = "操作开始时间", example = "1734370081000")
    private Long operationStartTime;

    @Schema(description = "操作结束时间", example = "1734449281000")
    private Long operationEndTime;

    @Schema(description = "分页与排序")
    private PageParams pageParams;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getOperationStartTime() {
        return operationStartTime;
    }

    public void setOperationStartTime(Long operationStartTime) {
        this.operationStartTime = operationStartTime;
    }

    public Long getOperationEndTime() {
        return operationEndTime;
    }

    public void setOperationEndTime(Long operationEndTime) {
        this.operationEndTime = operationEndTime;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    /**
     *
     */
    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (StringUtils.isBlank(bookId)) {
            result.addError("bookId", "教材ID不能为空");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }
    }
}

