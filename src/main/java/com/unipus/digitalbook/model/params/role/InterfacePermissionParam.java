package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.RolePermissionItem;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "接口权限参数")
public class InterfacePermissionParam implements Params {
    @Schema(description = "接口路径")
    private String interfacePath;
    @Schema(description = "true分配，false取消分配")
    private boolean assigned;

    public RolePermissionItem toEntity() {
        RolePermissionItem rolePermissionItem = new RolePermissionItem();
        rolePermissionItem.setPermissionReference(interfacePath);
        rolePermissionItem.setEnable(assigned);
        return rolePermissionItem;
    }

    public String getInterfacePath() {
        return interfacePath;
    }

    public void setInterfacePath(String interfacePath) {
        this.interfacePath = interfacePath;
    }

    public boolean isAssigned() {
        return assigned;
    }

    public void setAssigned(boolean assigned) {
        this.assigned = assigned;
    }

    @Override
    public void valid() {
        if (interfacePath == null) {
            throw new IllegalArgumentException("接口路径不能为空");
        }
    }
}
