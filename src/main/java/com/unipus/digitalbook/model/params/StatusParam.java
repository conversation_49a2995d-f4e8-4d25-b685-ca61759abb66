package com.unipus.digitalbook.model.params;

import com.unipus.digitalbook.model.enums.StatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "修改状态参数")
public class StatusParam implements Params {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "状态")
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public void valid() {
        if (this.getId() == null || this.getId() <= 0) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (StatusEnum.getStatus(this.getStatus()) == null) {
            throw new IllegalArgumentException("状态参数错误");
        }
    }
}
