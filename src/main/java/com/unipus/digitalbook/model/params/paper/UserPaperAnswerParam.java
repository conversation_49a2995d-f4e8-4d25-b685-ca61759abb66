package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.question.UserAnswerParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户试卷作答记录参数
 */
@Schema(description = "用户试卷作答记录参数")
public class UserPaperAnswerParam implements Params {

    @Schema(description = "试卷实例ID/轮次ID")
    private String instanceId;

    @Schema(description = "试卷类型：1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;

    @Schema(description = "诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)")
    private Integer testMode;

    @Schema(description = "推荐卷诊断模式时，原推荐卷实例ID(仅诊断卷，推荐模式时赋值)")
    private String baseInstanceId;

    @Schema(description = "用户作答记录映射(大题业务ID作为Key, 作答记录列表作为Value)")
    private Map<String, List<UserAnswerParam>> userAnswerParamsMap;

    @Schema(description = "是否异步处理: false:同步/true:异步")
    private Boolean async = false;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.instanceId)) {
            throw new IllegalArgumentException("试卷实例ID不能为空");
        }
        if (this.paperType == null) {
            throw new IllegalArgumentException("试卷类型不能为空");
        }
        if (PaperTypeEnum.getByCode(this.paperType) == null) {
            throw new IllegalArgumentException("无效的试卷类型");
        }
        if (CollectionUtils.isEmpty(this.userAnswerParamsMap)) {
            throw new IllegalArgumentException("用户作答记录不能为空");
        }
        this.userAnswerParamsMap.values().forEach(ua -> ua.forEach(UserAnswerParam::valid));
    }

    /**
     * 转换用户作答记录
     * @param userAccessInfo 用户访问信息
     * @param versionNumber 试卷版本号
     * @return 用户作答记录
     */
    public Map<String, List<UserAnswer>> toUserAnswerMap(UserAccessInfo userAccessInfo, String versionNumber) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();
        String envPartition = userAccessInfo.getEnvPartition();

        if (CollectionUtils.isEmpty(this.userAnswerParamsMap)) {
            return Collections.emptyMap();
        }
        Map<String, List<UserAnswer>> userAnswerMap = new HashMap<>();
        this.userAnswerParamsMap.forEach((questionGroupBizId,params) -> {
            List<UserAnswer> userAnswers = params.stream()
                    // 转换为用户作答记录(this.instanceId作为题目的batchId)
                    .map(u -> u.toEntity(tenantId, envPartition, openId, versionNumber, this.instanceId))
                    .collect(Collectors.toList());
            userAnswerMap.put(questionGroupBizId, userAnswers);
        });
        return userAnswerMap;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Integer getTestMode() {
        return testMode;
    }

    public void setTestMode(Integer testMode) {
        this.testMode = testMode;
    }

    public String getBaseInstanceId() {
        return baseInstanceId;
    }

    public void setBaseInstanceId(String baseInstanceId) {
        this.baseInstanceId = baseInstanceId;
    }

    public Map<String, List<UserAnswerParam>> getUserAnswerParamsMap() {
        return userAnswerParamsMap;
    }

    public void setUserAnswerParamsMap(Map<String, List<UserAnswerParam>> userAnswerParamsMap) {
        this.userAnswerParamsMap = userAnswerParamsMap;
    }

    public Boolean getAsync() {
        return async;
    }

    public void setAsync(Boolean async) {
        this.async = async;
    }
}
