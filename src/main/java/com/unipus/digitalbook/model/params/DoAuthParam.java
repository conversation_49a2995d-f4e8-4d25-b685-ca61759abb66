package com.unipus.digitalbook.model.params;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: 中文输入法发挥不稳定的刘川
 * @Date: 2024/10/16 下午4:50
 * <p>
 * 认证参数类
 * 此类用于封装与认证相关的参数，包括服务票据和授权票据。
 */

@Schema(description = "认证参数类")
public class DoAuthParam {
    /**
     * 服务票据
     * 服务票据是用于访问特定服务的凭证，通常由认证服务器颁发。
     */
    @Schema(description = "服务票据，用于访问特定服务的凭证", example = "ST-123456")
    private String serviceTicket;

    /**
     * 授权票据
     * 授权票据是用于获取服务票据的凭证，通常由认证服务器在用户首次登录时颁发。
     * 它可以用于多次获取服务票据，而无需用户再次登录。
     */
    @Schema(description = "授权票据，用于获取服务票据的凭证", example = "TGT-123456")
    private String ticketGrantingTicket;

    public String getServiceTicket() {
        return serviceTicket;
    }

    public void setServiceTicket(String serviceTicket) {
        this.serviceTicket = serviceTicket;
    }

    public String getTicketGrantingTicket() {
        return ticketGrantingTicket;
    }

    public void setTicketGrantingTicket(String ticketGrantingTicket) {
        this.ticketGrantingTicket = ticketGrantingTicket;
    }
}





