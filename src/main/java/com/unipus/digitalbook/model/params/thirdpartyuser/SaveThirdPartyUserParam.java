package com.unipus.digitalbook.model.params.thirdpartyuser;

import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.BeanUtils;

@Schema(description = "保存第三方用户参数")
public class SaveThirdPartyUserParam {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 用户openId
     */
    @Schema(description = "用户openId")
    private String openId;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickName;

    /**
     * 用户全称
     */
    @Schema(description = "用户全称")
    private String fullName;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址")
    private String email;

    /**
     * 是否有效 0-无效 1-有效
     */
    @Schema(description = "是否有效 0-无效 1-有效")
    private Boolean enable;

    public ThirdPartyUserInfo toEntity() {
        ThirdPartyUserInfo thirdPartyUserInfo = new ThirdPartyUserInfo();
        BeanUtils.copyProperties(this, thirdPartyUserInfo);
        return thirdPartyUserInfo;
    }
}
