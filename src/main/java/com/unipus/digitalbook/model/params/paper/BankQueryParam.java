package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 题库信息查询参数
 */
@Schema(description = "题库信息查询参数")
public class BankQueryParam implements Params {

    @Schema(description = "试卷ID")
    private String paperId;

    @Schema(description = "题库版本")
    private String versionNumber;

    @Override
    public void valid() {
        if(!StringUtils.hasText(paperId)){
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        // todo: 暂时默认版本号
        if(!StringUtils.hasText(versionNumber)){
            //throw new IllegalArgumentException("试卷版本不能为空");
            this.versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        }
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
