package com.unipus.digitalbook.model.params;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.StringJoiner;

@Schema(description = " 入参：通过id列表，批量改变状态")
public class ChangeStatusParam implements Params{
    @Schema(description = "id列表")
    List<Long> idList;
    @Schema(description = "// 状态枚举：ACTIVE(1, \"启用\") - 启用, INACTIVE(0, \"禁用\") - 禁用, PENDING_APPROVAL(3, \"审核中\") - 审核中", example = "0")
    Integer status;

    public List<Long> getIdList() {
        return idList;
    }

    public ChangeStatusParam setIdList(List<Long> idList) {
        this.idList = idList;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public ChangeStatusParam setStatus(Integer status) {
        this.status = status;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ChangeStatusParam.class.getSimpleName() + "[", "]")
                .add("idList=" + idList)
                .add("status=" + status)
                .toString();
    }

    /**
     * 检验参数合法性
     */
    @Override
    public void valid() {
        if (idList == null || idList.isEmpty()) {
            throw new IllegalArgumentException("idList不能为空");
        }
        switch (status) {
            case 1:
            case 0:
            case 3:
                break;
            default:
                throw new IllegalArgumentException("status必须是ACTIVE(1), INACTIVE(0), 或PENDING_APPROVAL(3)");
        }
    }
}
