package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "资源分享用户查询参数")
public class ResourceUserSearchParam implements Params {

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "检索关键字(用户名、手机号)")
    private String keyword;

    @Schema(description = "分页参数")
    private PageParams pageParams;
    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public void valid() {
        if(!StringUtils.hasText( this.resourceId)){
            throw new IllegalArgumentException("资源ID不能为空");
        }
        if (this.pageParams == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
    }

}
