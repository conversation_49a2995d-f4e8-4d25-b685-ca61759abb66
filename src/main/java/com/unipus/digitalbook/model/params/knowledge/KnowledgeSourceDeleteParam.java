package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceDeleteParam implements Params {

    /**
     * 图谱Id
     */
    private String knowledgeId;
    /**
     * 课程资源主键
     */
    private Long courseResourceId;

    /**
     * 三方资源Id
     */
    private String thirdResourceId;

    @Override
    public void valid() {
// 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (null == this.getCourseResourceId()) {
            result.addError("教材知识图谱关系Id为空", "请输入教材知识图谱关系Id为空");
        }

        if (null == this.getThirdResourceId()) {
            result.addError("三方资源Id为空", "请输入三方资源Id");
        }

        if (StringUtils.isBlank(this.getKnowledgeId()) ){
            result.addError("三方图谱Id为空", "请输入三方图谱Id");
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }
}
