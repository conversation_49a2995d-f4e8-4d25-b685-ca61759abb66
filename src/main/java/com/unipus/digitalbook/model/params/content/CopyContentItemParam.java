package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.entity.content.CopyContentItem;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "复制内容项参数")
public class CopyContentItemParam implements Params {

    @Schema(description = "自建内容业务ID")
    private String bizId;

    @Schema(description = "内容数据包")
    private String contentPackage;

    public CopyContentItemParam() {
    }

    public CopyContentItemParam(String bizId, String contentPackage) {
        this.bizId = bizId;
        this.contentPackage = contentPackage;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getContentPackage() {
        return contentPackage;
    }

    public void setContentPackage(String contentPackage) {
        this.contentPackage = contentPackage;
    }

    public CopyContentItem toEntity() {
        return new CopyContentItem(this.bizId, this.contentPackage);
    }

    @Override
    public void valid() {
        if (StringUtils.isBlank(bizId)) {
            throw new IllegalArgumentException("复制内容项的bizId不能为空");
        }
    }
}
