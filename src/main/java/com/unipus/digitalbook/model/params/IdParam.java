package com.unipus.digitalbook.model.params;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

@Schema(description = "ID参数类")
public class IdParam<T> implements Params{
    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private T id;

    public T getId() {
        return id;
    }

    public void setId(T id) {
        this.id = id;
    }

    /**
     *
     */
    @Override
    public void valid() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (id instanceof Integer idInteger) {
            if (idInteger <= 0) {
                throw new IllegalArgumentException("id不能小于等于0");
            }
        } else if (id instanceof Long idLong) {
            if (idLong <= 0) {
                throw new IllegalArgumentException("id不能小于等于0");
            }
        } else if (id instanceof String idString) {
            if (idString.isEmpty()) {
                throw new IllegalArgumentException("id不能为空");
            }
        } else {
            throw new IllegalArgumentException("id类型错误");
        }

    }
}
