package com.unipus.digitalbook.model.params.publish;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "教材发布上架版本查询参数")
public class BookPublishedVersionParam implements Params {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "发布上架开始时间", example = "1734370081000")
    private Long publishStartTime;

    @Schema(description = "发布上架结束时间", example = "1734449281000")
    private Long publishEndTime;

    @Schema(description = "分页与排序")
    private PageParams pageParams;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Long getPublishStartTime() {
        return publishStartTime;
    }

    public void setPublishStartTime(Long publishStartTime) {
        this.publishStartTime = publishStartTime;
    }

    public Long getPublishEndTime() {
        return publishEndTime;
    }

    public void setPublishEndTime(Long publishEndTime) {
        this.publishEndTime = publishEndTime;
    }

    public PageParams getPageParams() {
        return pageParams;
    }

    public void setPageParams(PageParams pageParams) {
        this.pageParams = pageParams;
    }

    @Override
    public void valid() {
        if (bookId == null) {
            throw new IllegalArgumentException("bookId不能为空");
        }
    }
}
