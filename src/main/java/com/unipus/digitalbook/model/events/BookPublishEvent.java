package com.unipus.digitalbook.model.events;

/**
 * 书籍发布事件类，用于记录书籍发布事件信息。
 * 包含书籍ID、版本号、版本ID以及操作用户ID等属性。
 */
public class BookPublishEvent {
    // 书籍唯一标识符
    private String bookId;
    // 书籍版本号
    private String versionNumber;
    // 书籍版本唯一标识符
    private Long versionId;
    // 操作用户唯一标识符
    private Long oprUserId;
    //  操作用户注册sso唯一标识符
    private String openId;

    public BookPublishEvent(String bookId, String versionNumber, Long versionId,Long oprUserId,  String openId) {
        this.bookId = bookId;
        this.versionNumber = versionNumber;
        this.versionId = versionId;
        this.oprUserId = oprUserId;
        this.openId = openId;
    }


    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public Long getOprUserId() {
        return oprUserId;
    }

    public void setOprUserId(Long oprUserId) {
        this.oprUserId = oprUserId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}

