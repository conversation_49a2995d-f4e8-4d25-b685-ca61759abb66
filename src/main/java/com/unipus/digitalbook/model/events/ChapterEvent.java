package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import org.springframework.context.ApplicationEvent;

public class ChapterEvent extends ApplicationEvent {
    private String bookId;

    private String chapterId;

    private EventTypeEnum eventType;

    private Long opsUserId;

    public ChapterEvent(Object source, String bookId, String chapterId, EventTypeEnum eventType, Long opsUserId) {
        super(source);
        this.chapterId = chapterId;
        this.bookId = bookId;
        this.eventType = eventType;
        this.opsUserId = opsUserId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public EventTypeEnum getEventType() {
        return eventType;
    }

    public void setEventType(EventTypeEnum eventType) {
        this.eventType = eventType;
    }

    public Long getOpsUserId() {
        return opsUserId;
    }

    public void setOpsUserId(Long opsUserId) {
        this.opsUserId = opsUserId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
}
