package com.unipus.digitalbook.model.events;

public class UaiResourcePublishEvent {
    /**
     * 教材id，即bookId
     */
    private String resourceId;

    /**
     * 教材版本号
     */
    private String version;

    /**
     * 教材状态
     */
    private Integer state;

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
