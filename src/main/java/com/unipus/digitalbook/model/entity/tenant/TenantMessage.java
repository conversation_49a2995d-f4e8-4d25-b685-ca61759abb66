package com.unipus.digitalbook.model.entity.tenant;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class TenantMessage<S> implements Serializable {
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 消息种类
     */
    private String messageTopic;

    /**
     * 消息
     */
    private String message;

    /**
     * 消息唯一标识
     */
    private String messageUuid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 消息是否异步
     */
    private Boolean async;

    /**
     * message object
     */
    private S messageObj;

    public TenantMessage() {

    }

    @SneakyThrows
    public TenantMessage(Long tenantId, String messageTopic, String messageUuid, Boolean async, S messageObj) {
        setTenantId(tenantId);
        setMessageTopic(messageTopic);
        setMessageUuid(messageUuid);
        setCreateBy(0L);
        setUpdateBy(0L);
        setMessage(JsonUtil.writeValueAsString(messageObj));
        setAsync(async);
        setMessageObj(messageObj);
    }

    public TenantMessagePO toPO() {
        TenantMessagePO tenantMessagePO = new TenantMessagePO();
        tenantMessagePO.setId(getId());
        tenantMessagePO.setMessage(getMessage());
        tenantMessagePO.setMessageTopic(getMessageTopic());
        tenantMessagePO.setMessageUuid(getMessageUuid());
        tenantMessagePO.setTenantId(getTenantId());
        tenantMessagePO.setCreateBy(getCreateBy());
        tenantMessagePO.setCreateTime(getCreateTime());
        tenantMessagePO.setEnable(getEnable());
        tenantMessagePO.setUpdateBy(getUpdateBy());
        tenantMessagePO.setUpdateTime(getUpdateTime());
        tenantMessagePO.setAsync(getAsync());
        tenantMessagePO.setRetryTimes(0);
        return tenantMessagePO;
    }
}
