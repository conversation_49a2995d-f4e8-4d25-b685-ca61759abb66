package com.unipus.digitalbook.model.entity.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Schema(description = "题库统计信息类")
public class QuestionBankStat {
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "全题库题目总数")
    private Integer questionTotalCount;
    @Schema(description = "每轮题目总数")
    private Integer challengeRoundCount;
    @Schema(description = "每轮总分")
    private Integer challengeRoundScore;

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getQuestionTotalCount() {
        return questionTotalCount;
    }

    public void setQuestionTotalCount(Integer questionTotalCount) {
        this.questionTotalCount = questionTotalCount;
    }

    public Integer getChallengeRoundCount() {
        return challengeRoundCount;
    }

    public void setChallengeRoundCount(Integer challengeRoundCount) {
        this.challengeRoundCount = challengeRoundCount;
    }

    public Integer getChallengeRoundScore() {
        return challengeRoundScore;
    }

    public void setChallengeRoundScore(Integer challengeRoundScore) {
        this.challengeRoundScore = challengeRoundScore;
    }

    /**
     * 统计题库信息
     * @param questionBankList 题库对象列表
     * @return 题库统计信息
     */
    public static QuestionBankStat build(List<QuestionBank> questionBankList) {
        if(CollectionUtils.isEmpty(questionBankList)){
            return null;
        }
        // 【试卷ID】
        String paperId = questionBankList.getFirst().getPaperId();
        // 【全题库题目总数】已创建题库中所有题目的总数
        int questionTotalCount = 0;
        // 【每轮题目总数】每个题库中设置的每关题目数量的总计
        int challengeRoundCount = 0;
        // 【每轮总分】每个题库中设置的每关题目分数总计
        int challengeRoundScore = 0;
        // 遍历题库列表进行统计
        for (QuestionBank qb : questionBankList) {
            questionTotalCount += (qb.getQuestionCount() != null ? qb.getQuestionCount() : 0);
            challengeRoundCount += (qb.getQuestionsPerRound() != null ? qb.getQuestionsPerRound() : 0);
            challengeRoundScore += (qb.getQuestionScore() != null && qb.getQuestionsPerRound() != null ?
                    qb.getQuestionScore() * qb.getQuestionsPerRound() : 0);
        }

        // 构建题库统计信息
        QuestionBankStat questionBankStat = new QuestionBankStat();
        questionBankStat.setPaperId(paperId);
        questionBankStat.setQuestionTotalCount(questionTotalCount);
        questionBankStat.setChallengeRoundCount(challengeRoundCount);
        questionBankStat.setChallengeRoundScore(challengeRoundScore);
        return questionBankStat;
    }

}
