package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Schema(description = "作答结果信息实体")
public class UserPaperAnswer {

    @Schema(description = "试卷成绩批次ID")
    private String scoreBatchId;

    @Schema(description = "试卷实例ID")
    private String instanceId;

    @Schema(description = "试卷总分")
    private BigDecimal totalScore;

    @Schema(description = "提交状态")
    private Integer submitStatus;

    @Schema(description = "正确答案（以大题ID为KEY）")
    private Map<String, List<QuestionAnswer>> correctAnswersMap;

    @Schema(description = "用户答案（以大题ID为KEY）")
    private Map<String, List<UserAnswer>> userAnswersMap;

    @Schema(description = "大题列表")
    private List<BigQuestionGroup> bigQuestionGroups;

    public UserPaperAnswer() {}

    // 用于构建正确答案
    public UserPaperAnswer(String instanceId, BigDecimal totalScore, List<BigQuestionGroup> bigQuestionGroups) {
        this.instanceId = instanceId;
        this.totalScore = totalScore;
        this.correctAnswersMap = buildCorrectAnswerMap(bigQuestionGroups);
        this.bigQuestionGroups = bigQuestionGroups;
    }

    // 用于构建用户答案
    public UserPaperAnswer(String scoreBatchId, String instanceId, BigDecimal totalScore, Map<String,
            List<UserAnswer>> userAnswersMap, Integer submitStatus) {
        this.scoreBatchId = scoreBatchId;
        this.instanceId = instanceId;
        this.totalScore = totalScore;
        this.submitStatus = submitStatus;
        this.userAnswersMap = userAnswersMap;
    }

    /**
     * 获取从题型里解析正确答案列表
     * @param bigQuestionGroups 大题组列表
     * @return 从题型里解析正确答案列表
     */
    private Map<String, List<QuestionAnswer>> buildCorrectAnswerMap(List<BigQuestionGroup> bigQuestionGroups) {
        if (CollectionUtils.isEmpty(bigQuestionGroups)) {
            return new HashMap<>();
        }
        return bigQuestionGroups.stream().collect(Collectors.toMap(BigQuestionGroup::getBizGroupId,
                        group -> group.fetchCorrectAnswers().values().stream().flatMap(List::stream).toList()));
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getSubmitStatus() {
        return submitStatus;
    }

    public void setSubmitStatus(Integer submitStatus) {
        this.submitStatus = submitStatus;
    }

    public Map<String, List<QuestionAnswer>> getCorrectAnswersMap() {
        return correctAnswersMap;
    }

    public void setCorrectAnswersMap(Map<String, List<QuestionAnswer>> correctAnswersMap) {
        this.correctAnswersMap = correctAnswersMap;
    }

    public Map<String, List<UserAnswer>> getUserAnswersMap() {
        return userAnswersMap;
    }

    public void setUserAnswersMap(Map<String, List<UserAnswer>> userAnswersMap) {
        this.userAnswersMap = userAnswersMap;
    }

    public List<BigQuestionGroup> getBigQuestionGroups() {
        return bigQuestionGroups;
    }

    public void setBigQuestionGroups(List<BigQuestionGroup> bigQuestionGroups) {
        this.bigQuestionGroups = bigQuestionGroups;
    }
}
