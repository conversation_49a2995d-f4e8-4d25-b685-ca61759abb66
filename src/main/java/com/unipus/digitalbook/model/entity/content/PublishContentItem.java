package com.unipus.digitalbook.model.entity.content;

/**
 * 发布内容项实体类
 */
public class PublishContentItem {

    /**
     * 自建内容业务ID
     */
    private String bizId;

    /**
     * 内容数据包
     */
    private String contentPackage;

    public PublishContentItem() {
    }

    public PublishContentItem(String bizId, String contentPackage) {
        this.bizId = bizId;
        this.contentPackage = contentPackage;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getContentPackage() {
        return contentPackage;
    }

    public void setContentPackage(String contentPackage) {
        this.contentPackage = contentPackage;
    }
}
