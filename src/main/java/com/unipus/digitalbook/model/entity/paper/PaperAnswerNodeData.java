package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerNode;
import com.unipus.digitalbook.model.enums.LearnTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PaperAnswerNodeData {
    /**
     * 答题人ID
     */
    private String openId;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 教材版本号
     */
    private String bookVersionNumber;

    /**
     * 答题人IP
     */
    private String ip;

    /**
     * 学习类型
     */
    private String learnType;
    /**
     * 答题数据
     */
    private UserPaperAnswerData data;

    public PaperAnswerNodeData(){}

    public PaperAnswerNodeData(UserPaperSyncInfo userPaperSyncInfo) {
        this.learnType = LearnTypeEnum.PAPER.getCode();
        this.openId = userPaperSyncInfo.getOpenId();
        this.bookId = userPaperSyncInfo.getBookId();
        this.bookVersionNumber = userPaperSyncInfo.getBookVersionNumber();
        this.ip = userPaperSyncInfo.getClientIp();
        this.data = new UserPaperAnswerData(userPaperSyncInfo);
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLearnType() {
        return learnType;
    }

    public void setLearnType(String learnType) {
        this.learnType = learnType;
    }

    public UserPaperAnswerData getData() {
        return data;
    }

    public void setData(UserPaperAnswerData data) {
        this.data = data;
    }

    public static class UserPaperAnswerData {

        private String paper ;

        private String dataPackage;

        public UserPaperAnswerData(){}

        public UserPaperAnswerData(UserPaperSyncInfo userPaperSyncInfo) {
            this.setDataPackage(userPaperSyncInfo.getDataPackage());
            this.setPaper(JsonUtil.toJsonString(new PaperAnswerData(userPaperSyncInfo)));
        }

        public String getPaper() {
            return paper;
        }

        public void setPaper(String paper) {
            this.paper = paper;
        }

        public String getDataPackage() {
            return dataPackage;
        }

        public void setDataPackage(String dataPackage) {
            this.dataPackage = dataPackage;
        }
    }

    static class PaperAnswerData {
        private String paperId;
        private String versionNumber;
        private Integer paperType;
        private BigDecimal score;
        private BigDecimal paperScore;
        private List<UserAnswerNode> answers;
        public PaperAnswerData(UserPaperSyncInfo syncInfo) {
            this.paperId = syncInfo.getPaperId();
            this.versionNumber = syncInfo.getPaperVersionNumber();
            this.paperType = syncInfo.getPaperType();
            this.score = syncInfo.getUserScore();
            this.paperScore = syncInfo.getTotalScore();
            List<UserAnswerNode> answers = new ArrayList<>();
            syncInfo.getQuestionGroups().forEach(question -> {
                List<UserAnswer> userAnswers = syncInfo.getUserAnswersMap().get(question.getBizGroupId());
                UserAnswerNode userAnswerNode = new UserAnswerNode().toUserAnswerNode(question, userAnswers);
                answers.add(userAnswerNode);
            });
            this.answers = answers;
        }

        public String getPaperId() {
            return paperId;
        }

        public void setPaperId(String paperId) {
            this.paperId = paperId;
        }

        public String getVersionNumber() {
            return versionNumber;
        }

        public void setVersionNumber(String versionNumber) {
            this.versionNumber = versionNumber;
        }

        public Integer getPaperType() {
            return paperType;
        }

        public void setPaperType(Integer paperType) {
            this.paperType = paperType;
        }

        public BigDecimal getScore() {
            return score;
        }

        public void setScore(BigDecimal score) {
            this.score = score;
        }

        public BigDecimal getPaperScore() {
            return paperScore;
        }

        public void setPaperScore(BigDecimal paperScore) {
            this.paperScore = paperScore;
        }

        public List<UserAnswerNode> getAnswers() {
            return answers;
        }

        public void setAnswers(List<UserAnswerNode> answers) {
            this.answers = answers;
        }
    }
}
