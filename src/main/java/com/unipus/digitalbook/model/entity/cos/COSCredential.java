package com.unipus.digitalbook.model.entity.cos;

import lombok.Data;

import java.io.Serializable;

/**
 * COS临时密钥凭证实体类
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
@Data
public class COSCredential implements Serializable {
    
    /**
     * 过期时间字符串
     */
    private String expiration;
    
    /**
     * 开始时间戳
     */
    private long startTime;
    
    /**
     * 过期时间戳
     */
    private long expiredTime;
    
    /**
     * 临时访问密钥ID
     */
    private String tmpSecretId;
    
    /**
     * 临时访问密钥
     */
    private String tmpSecretKey;
    
    /**
     * 会话令牌
     */
    private String sessionToken;

    public COSCredential() {
    }

    public COSCredential(String expiration, long startTime, long expiredTime, String tmpSecretId, String tmpSecretKey, String sessionToken) {
        this.expiration = expiration;
        this.startTime = startTime;
        this.expiredTime = expiredTime;
        this.tmpSecretId = tmpSecretId;
        this.tmpSecretKey = tmpSecretKey;
        this.sessionToken = sessionToken;
    }
}
