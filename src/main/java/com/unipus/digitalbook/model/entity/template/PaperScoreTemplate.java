package com.unipus.digitalbook.model.entity.template;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 模板信息
 */
@Data
public class PaperScoreTemplate {

    /**
     * 模板Id
     */
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板类型
     */
    private Integer type;

    /**
     * 模板状态
     */
    private Integer status;

    /**
     * 创建用户Id
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 模板详情
     */
    private List<PaperScoreTemplateDetail> templateDetailList;

    /**
     * 根据分数匹配模板详情
     * @param score 分数
     * @return 匹配的模板详情
     */
    public PaperScoreTemplateDetail getMatchedDetail(Integer score) {
        if  (CollectionUtils.isEmpty(this.templateDetailList)) {
            return null;
        }

        return this.templateDetailList.stream()
                .filter(detail ->
                        Objects.equals(detail.getMinScore(), score) && Objects.equals(detail.getMaxScore(), score) ||
                        detail.getMinScore() < score && score <= detail.getMaxScore()
                )
                .findFirst()
                .orElse(null);
    }
}
