package com.unipus.digitalbook.model.entity.complement;

/**
 * 配套资源引用实体
 */
public class ComplementResourceReference {

    /**
     * 主键
     */
    private Long id;

    /**
     * 配套资源ID
     */
    private String complementResourceId;

    /**
     * 媒体位置
     */
    private String position;

    /**
     * 章节id
     */
    private String chapterId;

    /**
     * 是否启用
     */
    private Boolean enable;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComplementResourceId() {
        return complementResourceId;
    }

    public void setComplementResourceId(String complementResourceId) {
        this.complementResourceId = complementResourceId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

}
