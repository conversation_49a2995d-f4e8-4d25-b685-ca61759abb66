package com.unipus.digitalbook.model.entity.user;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户信息数据
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/3 17:01
 */
public class SearchUser {

    /**
     * 用户ID，唯一标识每个用户。
     */
    private Long id;

    /**
     * 用户姓名，表示用户的名称。
     */
    private String name;

    /**
     * 手机号，用于联系用户的电话号码。
     */
    private String cellPhone;

    /**
     * 所属机构，表示用户机构。
     */
    private String org;

    /**
     * 所属机构Id，表示用户机构Id。
     */
    private Long orgId;

    /**
     * 角色列表，表示用户所拥有的角色集合。
     */
    private List<String> roleList;

    /**
     * 状态（0-未激活 1-已激活），表示用户是否已激活。
     */
    private Integer status;

    /**
     * 激活时间，表示用户激活的时间。
     */
    private Date activeTime;

    /**
     * 创建时间，表示用户记录创建的时间。
     */
    private Date createTime;

    public SearchUser() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public List<String> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<String> roleList) {
        this.roleList = roleList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
