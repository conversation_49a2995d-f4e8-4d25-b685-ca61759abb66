package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(description = "题库实体类")
public class QuestionBank {
    @Schema(description = "题库主键ID")
    private Long id;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "题库ID")
    private String bankId;
    @Schema(description = "题库名称")
    private String bankName;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "每题分数")
    private Integer questionScore;
    @Schema(description = "每轮随机抽取题目数")
    private Integer questionsPerRound;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "创建人ID")
    private Long creatorId;
    @Schema(description = "创建人名称")
    private String creatorName;
    @Schema(description = "试卷版本")
    private String versionNumber;

    public QuestionBank() {}

    public QuestionBank(BigQuestionGroup bigQuestion) {
        this.id = bigQuestion.getId();
        this.bankId = bigQuestion.getBizGroupId();
        this.bankName = bigQuestion.getQuestionText();
        this.questionCount = bigQuestion.getQuestions()==null ? 0 : bigQuestion.getQuestions().size();
        this.questionScore = bigQuestion.getScore()==null ? 0 : bigQuestion.getScore().intValue();
        this.content = bigQuestion.getContent();
        this.versionNumber = bigQuestion.getVersionNumber();
        this.creatorId = bigQuestion.getCreateBy();
        this.createTime = bigQuestion.getCreateTime();
    }

    public BigQuestionGroup buildQuestionGroup(Long parentId, Long userId){
        BigQuestionGroup questionGroup = new BigQuestionGroup();
        questionGroup.setBizGroupId(this.bankId);
        questionGroup.setParentId(parentId);
        questionGroup.setQuestionText(this.bankName);
        questionGroup.setType(QuestionGroupTypeEnum.QUESTION_BANK.getCode());
        // 题库内容
        questionGroup.setContent(this.content);
        questionGroup.setVersionNumber(this.versionNumber);
        questionGroup.setCreateBy(userId);
        questionGroup.setUpdateBy(userId);
        return questionGroup;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Integer questionScore) {
        this.questionScore = questionScore;
    }

    public Integer getQuestionsPerRound() {
        return questionsPerRound;
    }

    public void setQuestionsPerRound(Integer questionsPerRound) {
        this.questionsPerRound = questionsPerRound;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

}
