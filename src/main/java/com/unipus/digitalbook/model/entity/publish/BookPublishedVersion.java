package com.unipus.digitalbook.model.entity.publish;

import java.util.Date;

/**
 * 教材已发布版本实体类
 */
public class BookPublishedVersion {

    /**
     * 发布版本 ID
     */
    private Long id;

    /**
     * 教材 ID
     */
    private String bookId;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 格式版本号
     */
    private String formatVersionNum;

    /**
     * 上架时间
     */
    private Date publishTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建者
     */
    private String creatorName;

    public BookPublishedVersion(BookVersion bookVersion, String creatorName) {
        this.id = bookVersion.getId();
        this.bookId = bookVersion.getBookId();
        this.versionNum = bookVersion.getVersionNum();
        this.formatVersionNum = bookVersion.getShowVersionNumber();
        this.publishTime = bookVersion.getCreateTime();
        this.createBy = bookVersion.getCreateBy();
        this.creatorName = creatorName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public String getFormatVersionNum() {
        return formatVersionNum;
    }

    public void setFormatVersionNum(String formatVersionNum) {
        this.formatVersionNum = formatVersionNum;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
}
