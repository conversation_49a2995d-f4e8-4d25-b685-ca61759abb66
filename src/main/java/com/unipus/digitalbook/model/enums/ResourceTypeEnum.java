package com.unipus.digitalbook.model.enums;

/**
 * 资源类型类型
 */
public enum ResourceTypeEnum {

    DEFAULT(0, "任意"),
    BOOK(1, "教材"),
    CHAPTER(2, "章节");

    private final Integer code;
    private final String desc;

    ResourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResourceTypeEnum getResourceTypeEnum(Integer code) {
        for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
            if (resourceTypeEnum.getCode().equals(code)) {
                return resourceTypeEnum;
            }
        }
        return null;
    }
}
