package com.unipus.digitalbook.model.enums;

/**
 * 用户激活状态枚举类型。
 */
public enum UserActivationStatus {

    /**
     * 未激活状态 (0)。
     */
    INACTIVE(0, "未激活"),

    /**
     * 已激活状态 (1)。
     */
    ACTIVE(1, "已激活");

    private final int code;
    private final String description;

    /**
     * 构造方法。
     *
     * @param code        状态码
     * @param description 状态描述
     */
    UserActivationStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取状态码。
     *
     * @return 状态码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取状态描述。
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举值。
     *
     * @param code 状态码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果状态码无效
     */
    public static UserActivationStatus fromCode(int code) {
        for (UserActivationStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的用户激活状态码: " + code);
    }
}