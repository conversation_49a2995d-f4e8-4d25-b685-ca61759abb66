package com.unipus.digitalbook.model.enums;

public enum KnowledgeResourceEnableEnum {
    DISABLE(0, "无效"),
    ENABLE(1, "有效"),
    ;
    private final Integer code;
    private final String description;

    KnowledgeResourceEnableEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeResourceEnableEnum getStatus(Integer code) {
        for (KnowledgeResourceEnableEnum statusEnum : KnowledgeResourceEnableEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
