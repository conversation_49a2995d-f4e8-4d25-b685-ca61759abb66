package com.unipus.digitalbook.model.enums;

/**
 * <p>
 * 教材操作日志操作类型
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/17 14:31
 */
public enum BookOperationEnum {

    INSERT(1, "新增"),
    UPDATE(2, "编辑"),
    DELETE(3, "删除"),
    SAVE(4, "保存")
    ;

    private final Integer code;
    private final String desc;

    private static final BookOperationEnum[] values = values();

    BookOperationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static BookOperationEnum getByCode(Integer code) {
        for (BookOperationEnum bookOperationEnum : values) {
            if (bookOperationEnum.getCode().equals(code)) {
                return bookOperationEnum;
            }
        }
        return null;
    }

    /**
     * 根据desc获取枚举
     *
     * @param desc
     * @return
     */
    public static BookOperationEnum getByDesc(String desc) {
        for (BookOperationEnum operateObjectEnum : values) {
            if (operateObjectEnum.getDesc().equals(desc)) {
                return operateObjectEnum;
            }
        }
        return null;
    }
}
