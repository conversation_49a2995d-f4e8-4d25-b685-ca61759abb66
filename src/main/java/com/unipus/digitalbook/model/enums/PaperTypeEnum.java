package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 试卷类型枚举类
 * 定义系统支持的三种试卷类型及其属性
 */
@Getter
public enum PaperTypeEnum {
    /** 常规卷：标准的试卷类型，包含固定的题目列表 */
    REGULAR(1, "regular", "常规卷"),
    /** 挑战卷：从题库中随机抽取题目的试卷类型 */
    CHALLENGE(2, "challenge", "挑战卷"),
    /** 诊断卷：支持诊断和推荐两种模式的试卷类型 */
    DIAGNOSTIC(3, "diagnostic", "诊断卷");

    /** 类型编码 */
    private final Integer code;
    /** 类型名称 */
    private final String name;
    /** 类型描述 */
    private final String desc;

    PaperTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc =desc;
    }

    public static PaperTypeEnum getByCode(Integer code) {
        if(code == null) {
            return null;
        }
        for (PaperTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public Boolean match(Integer code){
        return this.getCode().equals(code);
    }

    public Boolean match(PaperTypeEnum type){
        return type==null ? Boolean.FALSE : this.getCode().equals(type.getCode());
    }

    public static QuestionGroupTypeEnum getQuestionGroupTypeByCode(Integer code) {
        if(code == 1) {
            return QuestionGroupTypeEnum.REGULAR_PAPER;
        }else
        if(code == 2) {
            return QuestionGroupTypeEnum.CHALLENGE_PAPER;
        }else
        if(code == 3) {
            return QuestionGroupTypeEnum.DIAGNOSTIC;
        }else {
            throw new IllegalArgumentException("Invalid code for QuestionGroupType: " + code);
        }
    }

    public static Integer getQuestionGroupTypeCodeByCode(Integer code) {
        if(code == null) {
            return null;
        }
        QuestionGroupTypeEnum questionGroupType = getQuestionGroupTypeByCode(code);
        return questionGroupType != null ? questionGroupType.getCode() : null;
    }

    public static PaperTypeEnum getTypeByGroupCode(Integer groupCode){
        if(QuestionGroupTypeEnum.REGULAR_PAPER.match(groupCode)) {
            return REGULAR;
        }else
        if(QuestionGroupTypeEnum.CHALLENGE_PAPER.match(groupCode)) {
            return CHALLENGE;
        }else
        if(QuestionGroupTypeEnum.DIAGNOSTIC.match(groupCode)) {
            return DIAGNOSTIC;
        }else {
            throw new IllegalArgumentException("Invalid code for QuestionGroupType: " + groupCode);
        }
    }
}
