package com.unipus.digitalbook.model.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 题目关系类型枚举类
 * 用于区分试卷中题目的关系类型，如普通题和推荐题
 */
@Getter
public enum QuestionRelationTypeEnum {
    /** 普通题：试卷中的常规题目 */
    COMMON(1, "common", "普通题"),
    /** 推荐题：基于诊断结果推荐给学生的题目 */
    RECOMMEND(2, "recommend", "推荐题");

    /** 类型编码 */
    private final Integer code;
    /** 类型名称 */
    private final String name;
    /** 类型描述 */
    private final String desc;

    QuestionRelationTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc =desc;
    }

    public static QuestionRelationTypeEnum getEnumByName(String name) {
        if (!StringUtils.hasText(name)){
            return null;
        }
        for (QuestionRelationTypeEnum typeEnum : values()) {
            if (typeEnum.getName().equals(name)){
                return typeEnum;
            }
        }
        return null;
    }

    public Boolean match(String name){
        return this.name.equals(name);
    }

}
