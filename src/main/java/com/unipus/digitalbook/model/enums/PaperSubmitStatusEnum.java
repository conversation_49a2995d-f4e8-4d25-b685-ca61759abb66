package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 试卷成绩提交状态举类
 */
@Getter
public enum PaperSubmitStatusEnum {
    UNSUBMITTED(0,"未提交"),
    SUBMITTED(1,"已提交");

    private final Integer code;
    private final String desc;

    PaperSubmitStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc =desc;
    }

    public static PaperSubmitStatusEnum getEnumByCode(Integer status) {
        for (PaperSubmitStatusEnum value : values()) {
            if (value.match(status)) {
                return value;
            }
        }
        return null;
    }

    public boolean match(Integer status) {
        return Objects.equals(this.code, status);
    }

    public boolean match(PaperSubmitStatusEnum status) {
        if (status == null) {
            return false;
        }
        return Objects.equals(this.code, status.getCode());
    }
}
