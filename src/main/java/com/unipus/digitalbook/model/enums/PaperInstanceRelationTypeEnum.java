package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 试卷实例关系类型枚举
 * 定义试卷实例之间的关系类型，主要用于诊断卷和推荐卷之间的关联
 */
 @Getter
public enum PaperInstanceRelationTypeEnum {
    /** 推荐关系：表示诊断卷实例与推荐卷实例之间的关联关系 */
    RECOMMEND(1, "推荐关系");

    /** 关系类型编码 */
    private final Integer code;
    /** 关系类型描述 */
    private final String desc;

    PaperInstanceRelationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PaperInstanceRelationTypeEnum of(Integer code) {
        for (PaperInstanceRelationTypeEnum relationTypeEnum : values()) {
            if (relationTypeEnum.code.equals(code)) {
                return relationTypeEnum;
            }
        }
        return null;
    }

    public boolean match(PaperInstanceRelationTypeEnum modeEnum) {
        return modeEnum == null ? Boolean.FALSE : modeEnum.match(this.code);
    }

    public boolean match(Integer status) {
        return Objects.equals(this.code, status);
    }
 }