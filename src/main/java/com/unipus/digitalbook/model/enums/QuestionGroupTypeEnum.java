package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 题组类型枚举
 */
@Getter
public enum QuestionGroupTypeEnum {

    SINGLE_CHOICE(1, "single_choice_group", "单选题组"),
    MULTI_CHOICE(2, "multi_choice_group", "多选题组"),
    SHORT_ANSWER(3, "short_answer_group", "简答题组"),
    MULTI_MEDIA_UPLOAD(4, "multi_media_upload_group", "多媒体上传题组"),
    TRANSLATION(5, "translation_group", "翻译题组"),
    WRITING(6, "writing_group", "写作题组"),
    ORAL_PERSONAL_STATE(7, "oral_personal_state_group", "个人陈述题组"),
    VOCABULARY(8, "vocabulary_group", "单词本题组"),
    FILL_BLANKS(9, "fill_blanks_group", "填空题组"),
    FILL_BLANKS_CHOICE(10, "fill_blanks_choice_group", "选词填空题组"),
    FILL_BLANKS_DROPDOWN(11, "fill_blanks_dropdown_group", "下拉选择填空题组"),
    DISCUSSION(12, "discussion_group", "讨论题组"),
    ORAL_SENTENCE_SCOOP(13, "oral_sentence_scoop_group", "句子跟读（挖空）题组"),
    SEQUENCE(14, "sequence_group", "排序题组"),
    RICH_TEXT_READ(15, "rich_text_read_group", "精读课文题组"),
    ROLE_PLAY_SCOOP(16, "role_play_scoop_group", "角色扮演（挖空)题组"),
    TEXT_MATCH(17, "text_match_group", "文本匹配组"),
    EVALUATION(18, "evaluation_group", "量表题组"),
    TRUE_FALSE(19, "true_false_group", "判断题组"),

    QUESTION_BANK(199, "question_bank_group", "题库"),
    REGULAR_PAPER(200, "regular_paper_group", "常规卷"),
    CHALLENGE_PAPER(201, "challenge_paper_group", "挑战卷"),
    DIAGNOSTIC(202, "diagnostic_paper_group", "诊断卷"),
    ;

    private static final Map<Integer, QuestionGroupTypeEnum> CODE_MAP = new HashMap<>();
    private static final Map<String, QuestionGroupTypeEnum> NAME_MAP = new HashMap<>();
    // 客观题
    private static final Map<Integer, QuestionGroupTypeEnum> OBJECTIVE_MAP = new HashMap<>();
    static {
        for (QuestionGroupTypeEnum type : QuestionGroupTypeEnum.values()) {
            CODE_MAP.put(type.getCode(), type);
            NAME_MAP.put(type.getName(), type);
        }
        // 初始化客观题映射
        OBJECTIVE_MAP.put(SINGLE_CHOICE.getCode(), SINGLE_CHOICE);
        OBJECTIVE_MAP.put(MULTI_CHOICE.getCode(), MULTI_CHOICE);
        OBJECTIVE_MAP.put(FILL_BLANKS.getCode(), FILL_BLANKS);
        OBJECTIVE_MAP.put(FILL_BLANKS_CHOICE.getCode(), FILL_BLANKS_CHOICE);
        OBJECTIVE_MAP.put(FILL_BLANKS_DROPDOWN.getCode(), FILL_BLANKS_DROPDOWN);
        OBJECTIVE_MAP.put(SEQUENCE.getCode(), SEQUENCE);
        OBJECTIVE_MAP.put(TEXT_MATCH.getCode(), TEXT_MATCH);
        OBJECTIVE_MAP.put(TRUE_FALSE.getCode(), TRUE_FALSE);
    }
    /**
     * 题型ID
     */
    private final Integer code;
    /**
     * 题型名称
     */
    private final String name;
    /**
     * 题型描述
     */
    private final String desc;

    QuestionGroupTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static QuestionGroupTypeEnum getEnumByName(String name) {
        return NAME_MAP.get(name);
    }

    public static QuestionGroupTypeEnum getEnumByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    public static String getEnumDescByCode(Integer code) {
        QuestionGroupTypeEnum typeEnum = CODE_MAP.get(code);
        return typeEnum == null ? "" : typeEnum.getDesc();
    }
    public static String getNameByCode(Integer code) {
        return CODE_MAP.get(code).getName();
    }

    public static Integer getCodeByName(String name) {
        for (QuestionGroupTypeEnum questionGroupTypeEnum : QuestionGroupTypeEnum.values()) {
            if (questionGroupTypeEnum.getName().equals(name)) {
                return questionGroupTypeEnum.getCode();
            }
        }
        return null;
    }

    // 判断题型是否客观题 true:是/false:否
    public static boolean isObjective(Integer code) {
        return OBJECTIVE_MAP.containsKey(code);
    }

    public boolean match(Integer code) {
        return this.getCode().equals(code);
    }

    public boolean isRealQuestionNode() {
        return !RICH_TEXT_READ.getName().equals(name) &&
                !DISCUSSION.getName().equals(name);
    }
}
