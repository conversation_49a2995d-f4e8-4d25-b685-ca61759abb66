package com.unipus.digitalbook.conf.cache;

import org.redisson.Redisson;
import org.redisson.api.NameMapper;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;

@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "redisson")
public class RedissonConfig {

    private List<String> nodes;

    private String password;

    private int scanInterval;

    @Value("${app.env}")
    private String env;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        Config config = new Config();
        String[] nodeAddresses = nodes.stream()
                .map(node -> "redis://" + node)
                .toArray(String[]::new);

        config.useClusterServers()
                .setPassword(password)
                .addNodeAddress(nodeAddresses)
                .setScanInterval(scanInterval)
                .setRetryAttempts(3)
                .setRetryInterval(1000)
                .setFailedSlaveReconnectionInterval(3000)
                .setMasterConnectionMinimumIdleSize(2)
                .setSlaveConnectionMinimumIdleSize(2)
                .setMasterConnectionPoolSize(10)
                .setSlaveConnectionPoolSize(10)
                .setSubscriptionConnectionMinimumIdleSize(1)
                .setSubscriptionConnectionPoolSize(5)
                .setTimeout(3000)
                .setConnectTimeout(3000)
                .setIdleConnectionTimeout(10000).setNameMapper(new NameMapper() {
                    @Override
                    public String map(String s) {
                        return env + ":" + s;
                    }
                    @Override
                    public String unmap(String s) {
                        return env + ":" + s;
                    }
                });
        config.setCodec(new CustomKryoCodec());
        return Redisson.create(config);
    }

    @Bean(name = "recentUseExecutor")
    public AsyncTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("RecentUse-");
        executor.initialize();
        return executor;
    }

    public List<String> getNodes() {
        return nodes;
    }

    public RedissonConfig setNodes(List<String> nodes) {
        this.nodes = nodes;
        return this;
    }

    public String getPassword() {
        return password;
    }

    public RedissonConfig setPassword(String password) {
        this.password = password;
        return this;
    }

    public int getScanInterval() {
        return scanInterval;
    }

    public RedissonConfig setScanInterval(int scanInterval) {
        this.scanInterval = scanInterval;
        return this;
    }

    @Bean(name = "clusterRedisConnectionFactory")
    public RedissonConnectionFactory clusterRedisConnectionFactory(RedissonClient redisson) {
        return new RedissonConnectionFactory(redisson);
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(@Qualifier("clusterRedisConnectionFactory") RedissonConnectionFactory redissonConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redissonConnectionFactory);
        template.setKeySerializer(new PrefixStringRedisSerializer(env + ":"));
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new PrefixStringRedisSerializer(env + ":"));
        template.setHashValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
}
