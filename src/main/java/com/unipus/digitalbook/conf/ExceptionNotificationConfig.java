package com.unipus.digitalbook.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 异常通知配置
 * 使用虚拟线程池确保异常通知能够完成执行
 */
@Slf4j
@Configuration
public class ExceptionNotificationConfig {

    /**
     * 异常通知虚拟线程池
     * 使用虚拟线程池确保异常通知能够完成执行，不会被主线程终止
     */
    @Bean("exceptionNotificationExecutor")
    public ExecutorService exceptionNotificationExecutor() {
        // 创建虚拟线程池，确保线程能够完成执行
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        
        log.info("异常通知虚拟线程池初始化完成");
        return executor;
    }
} 