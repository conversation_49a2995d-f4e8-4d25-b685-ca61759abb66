package com.unipus.digitalbook.conf.security;


import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.jwt.*;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.Date;

@Configuration
public class JwtConfig {

    @Resource
    JwtProperties jwtProperties;

    /**
     * 配置JWT解码器
     *
     * @return JWT解码器实例
     */
    @Bean
    public JwtDecoder jwtDecoder() {
        // 将JWT配置中的密钥字符串解码为字节数组
        byte[] secretKeyBytes = Base64.getDecoder().decode(jwtProperties.getSecret());

        // 使用解码后的字节数组创建一个HmacSHA256密钥
        SecretKey key = new SecretKeySpec(secretKeyBytes, "HmacSHA256");

        // 使用Nimbus库创建并返回一个基于该密钥的JWT解码器
        return NimbusJwtDecoder.withSecretKey(key).build();
    }

    @Bean("jwtEncoder")
    public JwtEncoder jwtEncoder() {
        return createJwtEncoder(jwtProperties.getSecret());
    }

    @Bean("coJwtEncoder")
    public JwtEncoder coJwtEncoder() {
        return createJwtEncoder(jwtProperties.getCoSecret());
    }

    /**
     * 使用提供的密钥创建JWT编码器。
     *
     * @param secretKey 用于JWT签名的Base64编码密钥
     * @return 配置了提供密钥的JwtEncoder实例
     */
    private JwtEncoder createJwtEncoder(String secretKey) {
        return parameters -> {
            try {
                // Decode the secret key and create a specification for HMAC-SHA256
                byte[] secretKeyBytes = Base64.getDecoder().decode(secretKey);
                SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyBytes, "HmacSHA256");

                // Create signer with the secret key
                MACSigner signer = new MACSigner(secretKeySpec);

                // Build JWT claims set from parameters
                JWTClaimsSet claimsSet = buildClaimsSet(parameters);

                // Create and sign the JWT
                JWSHeader header = new JWSHeader(JWSAlgorithm.HS256);
                SignedJWT signedJWT = new SignedJWT(header, claimsSet);
                signedJWT.sign(signer);

                // Build and return the final JWT
                return buildJwt(signedJWT, header, claimsSet);
            } catch (Exception e) {
                throw new IllegalStateException("Error while signing the JWT", e);
            }
        };
    }

    /**
     * 从编码器参数构建JWT声明集。
     *
     * @param parameters 包含声明的JWT编码器参数
     * @return 包含所有必需声明的JWTClaimsSet
     */
    private JWTClaimsSet buildClaimsSet(JwtEncoderParameters parameters) {
        JWTClaimsSet.Builder claimsSetBuilder = new JWTClaimsSet.Builder();
        parameters.getClaims().getClaims().forEach((key, value) ->
                claimsSetBuilder.claim(key, value instanceof Instant instant ? Date.from(instant) : value)
        );
        return claimsSetBuilder.build();
    }

    /**
     * 从已签名的JWT及其组件构建Jwt对象。
     *
     * @param signedJWT 已签名的JWT
     * @param header JWS头部
     * @param claimsSet JWT声明集
     * @return 包含序列化令牌及其元数据的Jwt对象
     */
    private Jwt buildJwt(SignedJWT signedJWT, JWSHeader header, JWTClaimsSet claimsSet) {
        return Jwt.withTokenValue(signedJWT.serialize())
                .header("alg", header.getAlgorithm().getName())
                .subject(claimsSet.getSubject())
                .issuer(claimsSet.getIssuer())
                .claims(claims -> claims.putAll(claimsSet.getClaims()))
                .issuedAt(claimsSet.getIssueTime() != null ? claimsSet.getIssueTime().toInstant() : null)
                .expiresAt(claimsSet.getExpirationTime() != null ? claimsSet.getExpirationTime().toInstant() : null)
                .build();
    }



}