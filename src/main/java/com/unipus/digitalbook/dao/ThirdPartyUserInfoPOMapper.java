package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO;

/**
 * <AUTHOR>
 * @description 针对表【third_party_user_info(第三方用户表)】的数据库操作Mapper
 * @createDate 2025-06-24 17:37:08
 * @Entity generator.domain.ThirdPartyUserInfoPO
 */
public interface ThirdPartyUserInfoPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ThirdPartyUserInfoPO record);

    int insertSelective(ThirdPartyUserInfoPO record);

    ThirdPartyUserInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ThirdPartyUserInfoPO record);

    int updateByPrimaryKey(ThirdPartyUserInfoPO record);

    ThirdPartyUserInfoPO selectByOpenIdAndTenantId(String openId, Long tenantId);

}
