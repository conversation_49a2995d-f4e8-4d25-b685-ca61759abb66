package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.complement.ComplementResourceReferencePO;

import java.util.List;

/**
 * 配套资源引用持久化对象
 */
public interface ComplementResourceReferencePOMapper {

    /**
     * 插入教材与媒体关系
     * @param complementResourceReferencePO 教材与媒体关系
     * @return 插入结果
     */
    Integer insertReference(ComplementResourceReferencePO complementResourceReferencePO);

    /**
     * 更新教材与媒体关系
     * @param complementResourceReferencePO 教材与媒体关系
     * @return 更新结果
     */
    Integer updateReference(ComplementResourceReferencePO complementResourceReferencePO);

    /**
     * 根据教材与媒体关系查询教材与媒体关系
     * @param complementResourceReferencePO 教材与媒体关系
     * @return 教材与媒体关系列表
     */
    List<ComplementResourceReferencePO> selectReference(ComplementResourceReferencePO complementResourceReferencePO);

}
