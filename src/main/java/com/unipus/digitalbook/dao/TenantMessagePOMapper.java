package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TenantMessagePOMapper {

    void insertMessage(TenantMessagePO tenantMessagePO);

    TenantMessagePO selectById(Long id);

    List<TenantMessagePO> selectByTenantIdAndMessageTopicLimitFromId(@Param("tenantId") Long tenantId,
                                                   @Param("messageTopic") String messageTopic,
                                                   @Param("id") Long id,
                                                   @Param("limit") Integer limit);

    void deleteById(Long id);

    void updateRetryTimes(Long id);
}
