package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChapterTemplatePOMapper {
    /**
     * 
     * 删除 chapter_template记录
     * 
     * @param id java.lang.Integer
     * @return int
     *
     * <AUTHOR>
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 
     * 新建 chapter_template记录
     * 
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO
     * @return int
     *
     * <AUTHOR>
     */
    int insert(ChapterTemplatePO row);

    /**
     * 
     * 新建 chapter_template记录
     * 
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO
     * @return int
     *
     * <AUTHOR>
     */
    int insertSelective(ChapterTemplatePO row);

    /**
     * 
     * 查询chapter_template记录
     * 
     * @param id java.lang.Integer
     * @return ChapterTemplatePO
     *
     * <AUTHOR>
     */
    ChapterTemplatePO selectByPrimaryKey(Integer id);

    /**
     * 
     * 更新 chapter_template记录
     * 
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO
     * @return int
     *
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(ChapterTemplatePO row);

    /**
     * 
     * 更新 chapter_template记录
     * 
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO
     * @return int
     *
     * <AUTHOR>
     */
    int updateByPrimaryKey(ChapterTemplatePO row);

    /**
     *
     * 根据chapterIdList查询chapter_template记录
     *
     * @param chapterIdList java.util.List<java.lang.String>
     * @return java.util.List<com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO>
     *
     * <AUTHOR>
     */
    List<ChapterTemplatePO> getChapterTemplateListByChapterIdList(@Param("chapterIdList") List<String> chapterIdList);
}