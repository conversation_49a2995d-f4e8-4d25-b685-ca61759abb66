package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.AsyncFailLogPO;
import org.apache.ibatis.annotations.Options;

import java.util.List;

/**
 * 异步处理异常日志操作
 */
public interface AsyncFailLogPOMapper {

    /**
     * 插入异常日志
     * @param asyncFailLogPO 异常日志
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertAsyncFailLog(AsyncFailLogPO asyncFailLogPO);

    /**
     * 根据id查询异常日志
     * @param asyncFailLogPO 异常日志查询参数
     * @return 异常日志
     */
    List<AsyncFailLogPO> selectAsyncFailLog(AsyncFailLogPO asyncFailLogPO);
    /**
     * 根据操作类型查询异常日志数量
     * @param operation 操作类型
     * @return 异常日志数量
     */
    int selectFailCountByOperation(String operation);

    /**
     * 根据操作类型查询异常日志
     * @param operation 操作类型
     * @return 异常日志
     */
    List<AsyncFailLogPO> getByOperation(String operation);
}
