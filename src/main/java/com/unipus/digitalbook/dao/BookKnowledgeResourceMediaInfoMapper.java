package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【book_knowledge_resource_media_info】的数据库操作Mapper
* @createDate 2025-07-07 15:55:20
* @Entity com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceMediaInfo
*/
public interface BookKnowledgeResourceMediaInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BookKnowledgeResourceMediaInfoPO record);

    int insertSelective(BookKnowledgeResourceMediaInfoPO record);

    BookKnowledgeResourceMediaInfoPO selectByPrimaryKey(Long id);

    List<BookKnowledgeResourceMediaInfoPO> selectByResourceIds(@Param("resourceIds") List<Long> resourceIds);

    int updateByPrimaryKeySelective(BookKnowledgeResourceMediaInfoPO record);

    int updateByResourceIdSelective(BookKnowledgeResourceMediaInfoPO record);

    int updateByPrimaryKey(BookKnowledgeResourceMediaInfoPO record);

}
