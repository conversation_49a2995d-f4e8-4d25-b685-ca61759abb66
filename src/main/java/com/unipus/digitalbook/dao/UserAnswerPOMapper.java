package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.UserAnswerPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【user_answer】的数据库操作Mapper
 * @createDate 2025-03-06 09:35:52
 * @Entity com.unipus.digitalbook.model.po.question.UserAnswerPO
 */
public interface UserAnswerPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(UserAnswerPO record);

    UserAnswerPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAnswerPO record);

    /**
     * 查询最近一次同一批次下的作答记录
     *
     * @param bizQuestionIdList     题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 最近一次作答记录列表
     */
    List<UserAnswerPO> selectLatest(@Param("bizQuestionIdList") Collection<String> bizQuestionIdList,
                                    @Param("questionVersionNumber") String questionVersionNumber,
                                    @Param("openId") String openId,
                                    @Param("tenantId") Long tenantId);

    /**
     * 查询作答记录
     *
     * @param bizQuestionIdList     题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 作答记录列表
     */
    List<UserAnswerPO> selectList(@Param("bizQuestionIdList") Collection<String> bizQuestionIdList,
                                    @Param("questionVersionNumber") String questionVersionNumber,
                                    @Param("openId") String openId,
                                    @Param("tenantId") Long tenantId);

    /**
     * 根据ID列表查询用户作答记录
     *
     * @param idList ID列表
     * @return 用户作答记录列表
     */
    List<UserAnswerPO> selectByIdList(@Param("idList") List<Long> idList);

    /**
     * 批量插入或更新用户作答记录
     * 以bizQuestionId和batch作为唯一标识
     *
     * @param list 用户作答记录列表
     * @return 影响的记录数
     */
    int batchInsert(List<UserAnswerPO> list);

    /**
     * 批量插入或更新用户作答记录
     * 以bizQuestionId和batch作为唯一标识
     *
     * @param list 用户作答记录列表
     * @return 影响的记录数
     */
    int batchInsertOrUpdate(List<UserAnswerPO> list);


    /**
     * 根据批次ID查询用户作答记录
     *
     * @param batchId 批次ID
     * @return 用户作答记录列表
     */
    List<UserAnswerPO> selectByBatchId(String batchId);

    /**
     * 批量通过用户作答记录
     *
     * @param ids ID列表
     */
    void batchPassUserAnswer(List<Long> ids);
}
