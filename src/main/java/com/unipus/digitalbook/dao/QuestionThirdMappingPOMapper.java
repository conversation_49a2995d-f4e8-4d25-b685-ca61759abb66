package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO;

/**
* <AUTHOR>
* @description 针对表【question_third_mapping(题组四方法映射表)】的数据库操作Mapper
* @createDate 2025-04-22 16:17:18
* @Entity com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO
*/
public interface QuestionThirdMappingPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(QuestionThirdMappingPO record);

    int insertSelective(QuestionThirdMappingPO record);

    QuestionThirdMappingPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionThirdMappingPO record);

    int updateByPrimaryKey(QuestionThirdMappingPO record);

}
