package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.QuestionAnswerPO;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【question_answer】的数据库操作Mapper
* @createDate 2025-03-06 09:34:09
* @Entity com.unipus.digitalbook.model.po.question.QuestionAnswerPO
*/
public interface QuestionAnswerPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(QuestionAnswerPO record);

    QuestionAnswerPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionAnswerPO record);

    int batchInsertOrUpdate(Collection<QuestionAnswerPO> list);

    List<QuestionAnswerPO> selectByQuestionId(Long questionId);

    List<QuestionAnswerPO> selectByQuestionIds(List<Long> questionIds);

    int deleteByIds(Collection<Long> ids, Long opsUserId);
}
