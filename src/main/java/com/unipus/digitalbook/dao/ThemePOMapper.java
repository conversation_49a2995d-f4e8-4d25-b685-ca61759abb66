package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.ThemePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教材主题表
 */
public interface ThemePOMapper {

    /**
     * 新增
     * @param themePO  教材主题
     * @return 新增ID
     */
    Long insert(ThemePO themePO);

    /**
     * 新增/更新
     * @param themePO  教材主题
     * @return 更新结果
     */
    Boolean update(ThemePO themePO);

    /**
     * 取得主题列表
     * @param ids 主题id列表
     * @return 主题列表
     */
    List<ThemePO> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 查询教材主题列表
     * @param themePO 查询条件
     * @return 教材主题列表
     */
    List<ThemePO> selectList(ThemePO themePO);

}