package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.po.assistant.AssistantPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssistantPOMapper {
    void upsertAssistant(AssistantPO assistantPO);

    void updateAssistant(AssistantPO assistantPO);

    List<AssistantPO> findByBookId(@Param("bookId") String bookId);

    List<AssistantPO> findByBookIdAndChapterId(@Param("bookId") String bookId, @Param("chapterId") String chapterId);

    void disableById(String id);

    AssistantPO selectById(String id);

    List<AssistantPO> findByConfigTypeAndTemplateId(@Param("configType") Integer configType,
                                                    @Param("templateId") String templateId,
                                                    @Param("page") PageParams pageParams);

    Integer countByConfigTypeAndTemplateId(@Param("configType") Integer configType,
                                           @Param("templateId") String templateId);
}
