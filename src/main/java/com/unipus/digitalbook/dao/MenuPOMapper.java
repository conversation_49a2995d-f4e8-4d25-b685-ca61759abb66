package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.menu.MenuPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【menu(菜单表)】的数据库操作Mapper
* @createDate 2024-11-29 00:50:57
* @Entity com.unipus.digitalbook.model.po.menu.MenuPO
*/
public interface MenuPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MenuPO record);

    int insertSelective(MenuPO record);

    MenuPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MenuPO record);

    int updateByPrimaryKey(MenuPO record);
    List<MenuPO> selectAll();
    int logicalDelete(Long updateBy, Long id);

    int findMaxPositionByParentId(Long parentId);

    int updatePosition (Long id, Integer position);

    int batchUpdatePosition(List<MenuPO> updateList);

    List<MenuPO> selectSubByParentId(Long parentId);
}
