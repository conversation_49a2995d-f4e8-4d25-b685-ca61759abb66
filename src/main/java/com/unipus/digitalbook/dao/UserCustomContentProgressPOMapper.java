package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_custom_content_progress(用户自定义内容进度表)】的数据库操作Mapper
* @createDate 2025-07-01 18:18:55
* @Entity com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO
*/
public interface UserCustomContentProgressPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserCustomContentProgressPO record);

    int insertSelective(UserCustomContentProgressPO record);

    UserCustomContentProgressPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserCustomContentProgressPO record);

    int updateByPrimaryKey(UserCustomContentProgressPO record);

    void saveProgressBit(Long tenantId, String openId, String contentId, byte[] progressBit);

    List<UserCustomContentProgressPO> getProgressList(Long tenantId, String contentId);

}
