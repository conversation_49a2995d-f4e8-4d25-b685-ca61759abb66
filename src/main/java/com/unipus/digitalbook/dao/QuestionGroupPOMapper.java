package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.QuestionBankPO;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【question_group】的数据库操作Mapper
* @createDate 2025-03-05 20:08:36
* @Entity com.unipus.digitalbook.model.po.question.QuestionGroupPO
*/
public interface QuestionGroupPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertOrUpdateSelective(QuestionGroupPO record);

    QuestionGroupPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionGroupPO record);

    /**
     * 根据业务组ID查询题组信息
     * 查询编辑中版本（版本号默认为0）
     * @param bizGroupId 业务
     * @return 题组信息
     */
    QuestionGroupPO selectQuestionGroupByBizGroupId(
            @Param("bizGroupId") String bizGroupId,
            @Param("versionNumber") @Nullable String versionNumber);

    /**
     * 通过题组业务ID查询下级题组列表
     * @param parentBizGroupId 题组ID
     * @return 题组信息
     */
    List<QuestionGroupPO> selectChildByParentBizGroupId(
            @Param("parentBizGroupId") String parentBizGroupId,
            @Param("versionNumber") String versionNumber);

    /**
     * 通过paper模板ID查询下级题库信息及题库下题目信息
     * @param paperId paper模板ID
     * @return 题组信息
     */
    List<QuestionBankPO> selectBankChildByPaperId(
            @Param("paperId") @Nullable String paperId,
            @Param("bankId") @Nullable String bankId,
            @Param("groupType") Integer groupType,
            @Param("versionNumber") String versionNumber);

    /**
     * 查询试卷下的题库信息
     * @param paperId paper模板ID
     * @return 题组信息
     */
    List<QuestionBankPO> selectBankChildSimpleInfo(
            @Param("paperId") @Nullable String paperId,
            @Param("versionNumber") String versionNumber);

    /**
     * 查询试卷下的题库信息
     * @param bankId 题库ID
     * @param versionNumber 版本号
     * @return 题组信息
     */
     QuestionBankPO selectBankDetail(
             @Param("bankId") String bankId,
             @Param("versionNumber") String versionNumber);

    /**
     * 根据业务组ID查询最新版本题组信息
     * @param bizGroupId 业务组ID
     * @return 题组信息
     */
    QuestionGroupPO selectLatestVersionByBizGroupId(String bizGroupId);

    /**
     * 根据父ID查询子题组信息
     * @param parentId 父ID
     * @return 子题组信息
     */
    List<QuestionGroupPO> selectByParentId(Long parentId);

    /**
     * 根据试卷ID查询所属题库下面的子题组信息
     * @param parentId 父ID
     * @return 子题组信息
     */
    List<QuestionGroupPO> selectQuestionByParentId(Long parentId);

    /**
     * 根据业务组ID和版本号查询题组信息
     * @param bizGroupId 业务组ID
     * @param versionNumber 版本号
     * @return 题组信息
     */
    QuestionGroupPO selectByBizGroupIdAndVersionNumber(String bizGroupId, String versionNumber);

    /**
     * 根据ID列表查询题组信息
     * @param ids ID列表
     * @return 题组信息
     */
    List<QuestionGroupPO> selectByIds(List<Long> ids);

    int deleteByIds(Collection<Long> ids, Long opsUserId);

    Long selectIdByBizParentIdAndVersionNumberAndBizGroupId(@Param("bizParentId") String bizParentId, @Param("parentVersionNumber") String parentVersionNumber, @Param("bizGroupId") String bizGroupId);

    /**
     * 通过题组业务ID取得上级题组信息
     * @param childBizGroupId 题组业务ID
     * @param versionNumber 题组版本
     * @return 上级题组对象
     */
    QuestionGroupPO selectParentByChildBizGroupId(
            @Param("childBizGroupId") String childBizGroupId,
            @Param("versionNumber") String versionNumber);
}
