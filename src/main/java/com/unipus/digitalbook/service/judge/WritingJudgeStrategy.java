package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.JudgeTaskTicket;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.WritingQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.service.remote.restful.engine.model.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.engine.EngineApiService;
import com.unipus.digitalbook.service.remote.restful.engine.model.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Set;

@Slf4j
@Component
public class WritingJudgeStrategy implements JudgeStrategy<WritingQuestion> {

    private static final String CACHE_KEY = "engin_%s";
    @Resource
    private EngineApiService engineApiService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.WRITING);
    }

    @Override
    public double judge(WritingQuestion question, UserAnswer userAnswer) {
        if (userAnswer == null) {
            return 0;
        }
        // 写作答案，纯文本
        if (!StringUtils.hasText(userAnswer.getBizAnswerId())) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String bizAnswerId = userAnswer.getBizAnswerId();
        String enginICorrectKey = String.format(CACHE_KEY, bizAnswerId);
        // 获取用户作答详情
        String evaluation = stringRedisTemplate.opsForValue().get(enginICorrectKey);
        if (!StringUtils.hasText(evaluation)) {
            throw new IllegalArgumentException("用户作答失效");
        }
        userAnswer.setEvaluation(evaluation);
        // 解析作答结果
        CorrectResult correctResult = JsonUtil.parseObject(evaluation, CorrectResult.class);
        if (correctResult == null || correctResult.getScore() == null) {
            log.error("写作作答结果数据异常: {}", correctResult);
            return 0;
        }
        Score total = correctResult.getScore().stream().filter(s -> s.getName().equals("total")).findAny().orElse(null);
        if (total == null) {
            return 0;
        }
        userAnswer.setScore(BigDecimal.valueOf(total.getValue()));
        return total.getValue() / 100d;
    }

    @Override
    public JudgeTaskTicket startJudgeTask(WritingQuestion question, UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        QuestionText questionText = question.getQuestionText();
        CorrectParam correctParam = new CorrectParam(userAnswer, questionText.getPlainText(), questionText.getAnswerWordLimit());
        log.info("questionId: {}, correctParam:{}", question.getBizQuestionId(), correctParam);
        // 发送评测请求
        BaseResponse<CorrectResponse> correctResponseBaseResponse = engineApiService.iCorrect(correctParam);
        log.info("questionId: {}, correctResponseBaseResponse:{}", question.getBizQuestionId(), correctResponseBaseResponse);
        if (!correctResponseBaseResponse.isSuccess()) {
            log.error("WritingJudge error questionId: {}", question.getBizQuestionId());
            throw new IllegalStateException("评测失败");
        }
        String iCorrectId = correctResponseBaseResponse.getData().getIcorrectId();
        String enginICorrectKey = String.format(CACHE_KEY, iCorrectId);
        // 记录用户作答详情
        stringRedisTemplate.opsForValue().set(enginICorrectKey, "", Duration.ofDays(1));
        // 如何把作答的id返回给前端，让前端使用这个id进行轮训判断是否有结果，有结果之后进行评测（judge）
        userAnswer.setBizAnswerId(iCorrectId);
        return new JudgeTaskTicket(iCorrectId, userAnswer);
    }

    @Override
    public void endJudgeTask(UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String bizAnswerId = userAnswer.getBizAnswerId();
        BaseResponse<CorrectResult> correctResultBaseResponse = engineApiService.get(bizAnswerId);
        log.info("bizAnswerId: {}, correctResultBaseResponse:{}", bizAnswerId, correctResultBaseResponse);
        String enginICorrectKey = String.format(CACHE_KEY, bizAnswerId);
        stringRedisTemplate.opsForValue().set(enginICorrectKey, JsonUtil.toJsonString(correctResultBaseResponse.getData()), Duration.ofDays(1));
   }

    @Override
    public UserAnswer fetchJudgeResult(UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String enginICorrectKey = String.format(CACHE_KEY, userAnswer.getBizAnswerId());
        String evaluation = stringRedisTemplate.opsForValue().get(enginICorrectKey);
        userAnswer.setEvaluation(evaluation);
        return userAnswer;
    }
}
