package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.TrueFalseQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class TrueFalseJudgeStrategy implements JudgeStrategy<TrueFalseQuestion> {
    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.TRUE_FALSE);
    }

    @Override
    public double judge(TrueFalseQuestion question, UserAnswer userAnswer) {
        List<QuestionAnswer> answers = question.getAnswers();
        if (CollectionUtils.isEmpty(answers)) {
            return 0;
        }
        QuestionAnswer answer = answers.getFirst();
        if (answer.getCorrectAnswerText().equals(userAnswer.getAnswer())) {
            return 1;
        }
        return 0;
    }
}
