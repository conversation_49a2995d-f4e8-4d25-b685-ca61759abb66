package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.FillBlankQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class BlankJudgeStrategy implements JudgeStrategy<FillBlankQuestion> {

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.FILL_BLANKS, QuestionTypeEnum.FILL_BLANKS_CHOICE, QuestionTypeEnum.FILL_BLANKS_DROPDOWN);
    }

    @Override
    public double judge(FillBlankQuestion question, UserAnswer userAnswer) {
        if (userAnswer.getAnswer() != null && !userAnswer.getAnswer().isEmpty()) {
            for (QuestionAnswer questionAnswer : question.getAnswers()) {
                if (userAnswer.getAnswer().trim().equals(questionAnswer.getCorrectAnswerText().trim())) {
                    return 1d;
                }
            }
        }
        return 0;
    }
}
