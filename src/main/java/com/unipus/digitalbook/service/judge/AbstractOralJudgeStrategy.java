package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.service.UserAnswerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 口语类评测策略的抽象基类
 *
 * @param <T> 题目类型参数
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOralJudgeStrategy<T extends Question> implements JudgeStrategy<T> {

    @Resource
    protected UserAnswerService userAnswerService;

    @Override
    public double judge(T question, UserAnswer userAnswer) {
        // 验证输入参数
        if (userAnswer == null) {
            return 0;
        }
        if (!StringUtils.hasText(userAnswer.getBizAnswerId())) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        // 获取评测结果
        JSONObject callbackBody = userAnswerService.getAnswerCallback(userAnswer.getBizAnswerId());
        if (callbackBody == null || callbackBody.isEmpty()) {
            throw new IllegalArgumentException("用户作答失效");
        }
        String evaluation = callbackBody.getString("evaluation");
        String bizQuestionId = callbackBody.getString("bizQuestionId");
        if (!StringUtils.hasText(evaluation)
                || !StringUtils.hasText(bizQuestionId) || !bizQuestionId.equals(userAnswer.getBizQuestionId())) {
            throw new IllegalArgumentException("用户作答失效");
        }
        userAnswer.setEvaluation(evaluation);
        // 计算得分
        BigDecimal total = total(evaluation);
        if (total != null && total.compareTo(BigDecimal.ZERO) > 0) {
            return total.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        }
        return 0;
    }

    /**
     * 子类实现，获取评测结果总分
     *
     * @param evaluation 评测结果
     * @return 评测结果总分
     */
    protected abstract BigDecimal total(String evaluation);
}
