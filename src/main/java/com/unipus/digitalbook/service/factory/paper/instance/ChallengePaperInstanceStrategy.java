package com.unipus.digitalbook.service.factory.paper.instance;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper;
import com.unipus.digitalbook.dao.PaperRoundPOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.dao.QuestionGroupPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.PaperInstanceCreationContext;
import com.unipus.digitalbook.model.entity.paper.QuestionBank;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.model.po.paper.QuestionBankPO;
import com.unipus.digitalbook.service.PaperService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 挑战卷实例策略
 */
@Service
@Slf4j
public class ChallengePaperInstanceStrategy extends AbstractPaperInstanceStrategy {

    @Resource
    private PaperService paperService;
    @Resource
    private PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    @Resource
    private PaperRoundPOMapper paperRoundPOMapper;
    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;
    @Resource
    private PaperQuestionInstancePOMapper paperQuestionInstancePOMapper;

    /**
     * 获取试卷类型
     * @return 试卷类型
     */
    @Override
    public PaperTypeEnum getPaperType() {
        return PaperTypeEnum.CHALLENGE;
    }

    /**
     * 生成试卷预览实例
     * @param context 试卷实例创建上下文
     * @return 预览模式试卷实例
     */
    @Override
    public PaperInstance createPreviewPaperInstance(PaperInstanceCreationContext context) {
        Paper paper = context.paper();
        String openId = context.openId();
        Long tenantId = context.tenantId();
        UnitTestModeEnum testMode = context.testMode();
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();
        // 获取挑战卷题库ID列表
        Set<String> bankIds = getQuestionBankIds(paperId, versionNumber);
        if(CollectionUtils.isEmpty(bankIds)){
            log.error("题库不存在:{}", paperId);
            throw new IllegalArgumentException("题库不存在");
        }
        // 获取题库题目组
        List<BigQuestionGroup> finalQuestionList = new ArrayList<>();
        for(String bankId : bankIds){
            finalQuestionList.addAll(paperService.getQuestions(bankId, versionNumber));
        }

        // 生成预览模式试卷
        return super.createPaperInstance(paper, finalQuestionList, generateScoreBatchId(), generateInstanceId(), openId, tenantId, testMode);
    }

    /**
     * 生成试卷真实实例
     * @param context 试卷实例创建上下文
     * @return 真实挑战卷实例
     */
    @Override
    public PaperInstance createRealPaperInstance(PaperInstanceCreationContext context) {
        Paper paper = context.paper();
        String openId = context.openId();
        Long tenantId = context.tenantId();
        UnitTestModeEnum testMode = context.testMode();
        List<BigQuestionGroup> questions = generateChallengeQuestionList(paper, openId, tenantId);
        String scoreBatchId = getLatestUncommitChallengePaperScoreBatchId(paper.getPaperId(), paper.getVersionNumber(), openId, tenantId);
        return super.createPaperInstance(paper, questions, scoreBatchId, null, openId, tenantId, testMode);
    }

    /**
     * 生成题目列表
     * @param paper 试卷对象
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @return 题目列表
     */
    private List<BigQuestionGroup> generateChallengeQuestionList(Paper paper, String openId, Long tenantId) {
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();

        // 0.取得用户大题作答次数映射
        Map<Long, Integer> usedQuestionGroupsCountMap = getUserChallengePaperUsedQuestionsCountMap(paperId, versionNumber, openId, tenantId);

        // 1.根据挑战卷模板ID获取题库列表（包含题库的配置信息）
        List<QuestionBank> bankList = getQuestionBankList(paperId, versionNumber);
        if (CollectionUtils.isEmpty(bankList)) {
            log.error("挑战卷 {} 未配置任何题库或配置获取失败", paperId);
            throw new IllegalArgumentException("挑战卷未配置题库");
        }

        List<BigQuestionGroup> collectedQuestions = new ArrayList<>();
        for (QuestionBank bank : bankList) {
            String bankId = bank.getBankId();
            String bankName = bank.getBankName();

            // 2.获取题库所有题目
            List<BigQuestionGroup> questionsInBank = paperService.getQuestions(bankId, versionNumber);
            if (CollectionUtils.isEmpty(questionsInBank)) {
                String message = MessageFormat.format("题库【{0}】未包含任何题目，无法抽取。", bankName);
                log.warn(message);
                throw new BizException(message);
            }

            // 3.遍历每个题库配置，获取根据轮数随机抽取题目
            // 每轮抽取题目数量
            int countPerRound = bank.getQuestionsPerRound();
            // 取得当前轮次所需题目
            List<BigQuestionGroup> drawnQuestions = generateCurrentRoundQuestions(bankId, questionsInBank,
                    countPerRound, usedQuestionGroupsCountMap);

            // 4.使用题库的分数作为题目分数
            BigDecimal scorePerQuestion = BigDecimal.valueOf(bank.getQuestionScore());
            drawnQuestions.forEach(q -> q.setScore(scorePerQuestion));

            // 5.添加到集合
            collectedQuestions.addAll(drawnQuestions);
            log.info("从题库 {} (BizGroupId: {}) 成功抽取 {} 道题目。", bankName, bankId, drawnQuestions.size());
        }

        if (collectedQuestions.isEmpty()) {
            log.error("未能从任何配置的题库中抽取到题目，无法生成挑战卷: {}", paperId);
            throw new IllegalArgumentException("未能抽取到任何题目，无法生成挑战卷");
        }

        // 5.返回所有抽取的题目
        return collectedQuestions;
    }


    /**
     * 获取题库列表
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @return 题库列表
     */
    private List<QuestionBank> getQuestionBankList(String paperId, String versionNumber) {
        // 通过试卷的业务ID查询题库列表
        Integer groupType = QuestionGroupTypeEnum.QUESTION_BANK.getCode();
        List<QuestionBankPO> questionBankPOs = questionGroupPOMapper.selectBankChildByPaperId(paperId, null, groupType, versionNumber);
        if (CollectionUtils.isEmpty(questionBankPOs)) {
            log.debug("当前试卷下没有题库信息:{}", paperId);
            return List.of();
        }
        // 按照题库创建时间升序排列输出
        return questionBankPOs.stream().sorted(Comparator.comparing(QuestionBankPO::getCreateTime))
                .map(bank->bank.toEntity(paperId)).toList();
    }

    /**
     * 获取题库ID列表
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @return 题库ID列表
     */
    private Set<String> getQuestionBankIds(String paperId, String versionNumber) {
        // 通过试卷的业务ID查询题库列表
        List<QuestionBankPO> questionBankPOs = questionGroupPOMapper.selectBankChildSimpleInfo(paperId, versionNumber);
        if (CollectionUtils.isEmpty(questionBankPOs)) {
            log.debug("当前试卷没有撇脂题库:{}", paperId);
            return Set.of();
        }
        // 按照题库创建时间升序排列输出
        return questionBankPOs.stream().sorted(Comparator.comparing(QuestionBankPO::getCreateTime))
                .map(QuestionBankPO::getBankId).collect(Collectors.toSet());
    }

    /**
     * 取得挑战测试最后一次成绩批次提交状态为【未提交】的成绩批次ID
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本
     * @param openId 用户OpenId
     * @param tenantId 租户ID
     * @return 成绩批次ID
     */
    private String getLatestUncommitChallengePaperScoreBatchId(String paperId, String versionNumber, String openId, Long tenantId) {
        // 查询挑战卷成绩批次表
        PaperScoreBatchPO condition = new PaperScoreBatchPO(paperId, null, openId, tenantId, null);
        PaperScoreBatchPO latestScoreBatchPO = paperScoreBatchPOMapper.getLatestPaperScoreBatch(condition);
        if(latestScoreBatchPO==null){
            log.debug("未找到未提交成绩记录:paperId={}，versionNumber={}， openId={}，tenantId={}", paperId, versionNumber, openId, tenantId);
            return null;
        }
        // 取得最后一次成绩批次提交状态为【未提交】的成绩批次ID
        return PaperSubmitStatusEnum.UNSUBMITTED.match(latestScoreBatchPO.getStatus()) ? latestScoreBatchPO.getId() : null;
    }

    /**
     * 生成挑战卷当前轮次的题目
     * @param bankId 题库ID
     * @param questionsInBank 题库题目集合
     * @param countPerRound 每轮抽取题目数量
     * @param usedQuestionGroupsCountMap 挑战测试题目作答次数映射（key：大题主键ID，value：用户已完成作答次数）
     * @return 抽取的题目列表
     */
    private List<BigQuestionGroup> generateCurrentRoundQuestions(String bankId,
            List<BigQuestionGroup> questionsInBank, Integer countPerRound, Map<Long, Integer> usedQuestionGroupsCountMap) {

        // 创建题目ID与作答次数映射
        Map<Long, Integer> usageMap = questionsInBank.stream().collect(Collectors.toMap(
                BigQuestionGroup::getId, q -> usedQuestionGroupsCountMap.getOrDefault(q.getId(), 0)));

        // 按提交次数对题目进行分组，使用次数升序排序(TreeMap默认排序)
        Map<Integer, List<BigQuestionGroup>> groupedQuestionsMap = questionsInBank.stream()
                .collect(Collectors.groupingBy(q -> usageMap.getOrDefault(q.getId(), 0), TreeMap::new, Collectors.toList()));

        // 按照作答次数从低到高依次选择题目
        List<BigQuestionGroup> drawnQuestions = new ArrayList<>();
        for (List<BigQuestionGroup> groups : groupedQuestionsMap.values()) {
            if (drawnQuestions.size() + groups.size() <= countPerRound) {
                drawnQuestions.addAll(groups);
            } else {
                Collections.shuffle(groups);
                int remaining = countPerRound - drawnQuestions.size();
                drawnQuestions.addAll(groups.subList(0, remaining));
                break;
            }
        }

        // 对抽取到的题目列表进行随机排序
        Collections.shuffle(drawnQuestions);

        // 4. 记录日志
        if(log.isDebugEnabled()) {
            log.debug("从题库 {} (BizGroupId: {}) 中抽取 {} 道题目。", bankId, bankId, drawnQuestions.size());
            outputLog(bankId, usageMap, drawnQuestions);
        }

        // 返回抽取到的题目列表
        return drawnQuestions;
    }

    // 输出出题累计次数
    private void outputLog(String bankId, Map<Long, Integer> usageMap, List<BigQuestionGroup> drawnQuestions){
        // 累加出题累计次数并标记被抽取题目
        drawnQuestions.forEach(q -> usageMap.merge(q.getId(), 1, Integer::sum));
        List<Long> ids = drawnQuestions.stream().map(BigQuestionGroup::getId).toList();

        // 构建日志信息
        StringBuilder sb = new StringBuilder(MessageFormat.format("bankId:[{0}]-> ", bankId));
        usageMap.forEach((k, v) ->
                sb.append(MessageFormat.format(" [{0}]:{1}{2}", k, v, ids.contains(k) ? "* " : "  ")));
        log.debug(sb.toString());
    }

    /**
     * 获取当前用户挑战卷已做大题数量映射
     * （挑战卷是按照大题单位抽取的，所以统计大题的作答次数即可）
     * @param paperId 试卷业务ID
     * @param versionNumber 试卷版本号
     * @param openId 用户OpenId
     * @param tenantId 租户ID
     * @return 用户作答记录
     */
    private Map<Long, Integer> getUserChallengePaperUsedQuestionsCountMap(
            String paperId, String versionNumber, String openId, Long tenantId) {

        // 查询用户答题记录（查询所有成绩批次的作答记录,包括已提交和未提交）
        List<PaperQuestionInstancePO> paperQuestionInstancePOs = paperQuestionInstancePOMapper
                .getUserAnswerRecord(paperId, null, openId, tenantId, null);
        if(CollectionUtils.isEmpty(paperQuestionInstancePOs)){
            log.debug("未能从数据库中查询到试卷答题记录，paperId: {}, versionNumber: {}", paperId, versionNumber);
            return Map.of();
        }
        // 取得用户挑战卷大题答题次数
        return paperQuestionInstancePOs.stream().collect(
                Collectors.toMap(PaperQuestionInstancePO::getQuestionGroupId, po -> 1, Integer::sum));
    }

    /**
     * 获取最近一次试卷实例信息
     * @param paperId 试卷ID
     * @param paperVersion 版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 诊断卷测试模式(1:诊断模式/2:推荐模式)
     * @param submitStatus 提交状态
     * @return 最近一次试卷实例信息
     */
    @Override
    protected PaperRoundPO getLatestInstanceInfo(String paperId, String paperVersion, String openId, Long tenantId, UnitTestModeEnum testMode, Integer submitStatus){
        // 查询最新一次试卷提交记录
        return paperRoundPOMapper.getLatestPaperInstance(paperId, null, openId, tenantId, submitStatus);
    }
}
