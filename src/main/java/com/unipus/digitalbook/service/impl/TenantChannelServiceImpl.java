package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.TenantChannelPOMapper;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.service.TenantChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TenantChannelServiceImpl implements TenantChannelService {

    private final TenantChannelPOMapper tenantChannelPOMapper;

    @Autowired
    public TenantChannelServiceImpl(TenantChannelPOMapper tenantChannelPOMapper) {
        this.tenantChannelPOMapper = tenantChannelPOMapper;
    }

    @Override
    public List<TenantChannelPO> selectByTenantIdAndMessageTopic(Long tenantId, String messageTopic) {
        return tenantChannelPOMapper.selectByTenantIdAndMessageTopic(tenantId, messageTopic);
    }
}
