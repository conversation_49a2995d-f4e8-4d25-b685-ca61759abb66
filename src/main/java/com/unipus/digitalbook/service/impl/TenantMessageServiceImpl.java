package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.TenantMessagePOMapper;
import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;
import com.unipus.digitalbook.service.TenantMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TenantMessageServiceImpl implements TenantMessageService {

    private final TenantMessagePOMapper tenantMessagePOMapper;

    @Autowired
    public TenantMessageServiceImpl(TenantMessagePOMapper tenantMessagePOMapper) {
        this.tenantMessagePOMapper = tenantMessagePOMapper;
    }

    @Override
    public void insertMessage(TenantMessagePO tenantMessagePO) {
        tenantMessagePOMapper.insertMessage(tenantMessagePO);
    }

    @Override
    public TenantMessagePO selectById(Long id) {
        return tenantMessagePOMapper.selectById(id);
    }

    @Override
    public List<TenantMessagePO> selectByTenantIdAndMessageTopicLimitFromId(Long tenantId, String messageTopic, Long id, Integer limit) {
        return tenantMessagePOMapper.selectByTenantIdAndMessageTopicLimitFromId(tenantId, messageTopic, id, limit);
    }

    @Override
    public void deleteById(Long id) {
        tenantMessagePOMapper.deleteById(id);
    }

    @Override
    public void updateRetryTimes(Long id) {
        tenantMessagePOMapper.updateRetryTimes(id);
    }
}
