package com.unipus.digitalbook.service.impl;

import com.google.common.collect.Maps;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.db.InsertFailedException;
import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.dto.book.BookBasicDTO;
import com.unipus.digitalbook.model.dto.book.BookCopyrightDTO;
import com.unipus.digitalbook.model.dto.book.BookIntroDTO;
import com.unipus.digitalbook.model.dto.book.BookSimpleDataDTO;
import com.unipus.digitalbook.model.entity.Series;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.*;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.*;
import com.unipus.digitalbook.model.params.book.BookSearchParam;
import com.unipus.digitalbook.model.params.book.LatestBookSearchParam;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.po.BookPublishPackagePO;
import com.unipus.digitalbook.model.po.book.*;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import com.unipus.digitalbook.model.po.publish.BookVersionPO;
import com.unipus.digitalbook.publisher.standalone.BookEventPublisher;
import com.unipus.digitalbook.publisher.standalone.ChapterEventPublisher;
import com.unipus.digitalbook.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BookServiceImpl implements BookService {

    @Resource
    private BookPOMapper bookPOMapper;
    @Resource
    private BookBasicPOMapper bookBasicPOMapper;
    @Resource
    private BookIntroPOMapper bookIntroPOMapper;
    @Resource
    private ChapterPOMapper chapterPOMapper;
    @Resource
    private BookVersionPOMapper bookVersionPOMapper;
    @Resource
    private ResourcePermissionService resourcePermissionService;
    @Resource
    private BookPermissionService bookPermissionService;
    @Resource
    private BookUserActivityPOMapper bookUserActivityPOMapper;
    @Resource
    private UserService userService;
    @Resource
    private BookOperationLogService operationLogService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private BookEventPublisher bookEventPublisher;

    @Resource
    private ChapterEventPublisher chapterEventPublisher;

    @Resource
    private BookCopyrightPOMapper copyrightPOMapper;

    @Resource
    private BookVersionInfoVersionRelationPOMapper bookVersionInfoVersionRelationPOMapper;
    @Resource
    private BookVersionChapterVersionRelationPOMapper bookVersionChapterVersionRelationPOMapper;
    @Resource
    private BookPublishPackagePOMapper bookPublishPackagePOMapper;
    @Resource
    private PaperVersionService paperVersionService;
    @Resource
    private SeriesService seriesService;

    private static final Integer MAX_TOP_N = 1000;

    private static final String DEFAULT_VERSION_NUMBER = "0";
    @Resource
    private ComplementResourceService complementResourceService;

    /**
     * 新增教材的方法。
     *
     * @param book 需要新增的教材对象
     * @return 新增教材的ID
     * @throws InsertFailedException 如果新增教材或教材简介失败时抛出此异常
     */
    @Override
    @Transactional
    public String addBook(Book book, Long creatorId) {
        book.generateId();
        // 将传入的Book对象转换为BookPO对象
        BookPO bookPO = new BookPO();
        bookPO.fromEntity(book);
        bookPO.setEditorId(creatorId);
        // 设置教材ID为UUID生成的字符串
        bookPO.setCreateBy(creatorId);
        // 插入教材信息到数据库
        int bookInsertRes = bookPOMapper.insertSelective(bookPO);
        if (bookInsertRes <= 0) {
            // 如果插入失败，记录错误日志并抛出自定义异常
            log.error("新增教材失败。入参{}", book);
            throw new InsertFailedException("新增教材失败");
        }

        // 创建教材基本信息对象，并设置关联教材ID
        BookBasicPO bookBasicPO = new BookBasicPO();
        bookBasicPO.fromEntity(book);
        bookBasicPO.setCreateBy(creatorId);
        // 插入基本信息到数据库
        int bookBasicInsertRes = bookBasicPOMapper.insertSelective(bookBasicPO);
        if (bookBasicInsertRes <= 0) {
            log.error("新增教材失败。入参:{}", bookBasicPO);
            throw new InsertFailedException("新增教教材基本信息失败");
        }

        // 创建教材简介对象，并设置关联教材ID
        String bookId = bookPO.getId();
        BookIntroPO introPO = new BookIntroPO();
        introPO.setBookId(bookPO.getId());
        introPO.setCreateBy(creatorId);
        // 插入教材简介信息到数据库
        int bookIntroInsertRes = bookIntroPOMapper.insertSelective(introPO);
        if (bookIntroInsertRes <= 0) {
            log.error("新增教材失败。入参:{}", introPO);
            throw new InsertFailedException("新增教材简介失败");
        }
        // 追加教材作者权限
        if (Boolean.FALSE.equals(bookPermissionService.addBookOwnerPermission(bookId, creatorId))) {
            log.error("追加教材作者权限失败。教材ID:{}", bookId);
            throw new BizException(ResultMessage.BIZ_RESOURCE_PERMISSION_ADD_FAIL, "教材");
        }
        // 发布教材新增消息
        bookEventPublisher.bookEventPublisher(bookPO.getId(), EventTypeEnum.ADD, creatorId);
        // 返回新增教材的ID
        return bookPO.getId();
    }

    /**
     * 添加章节到教材
     *
     * @param chapter  章节对象，包含章节的详细信息
     * @param createBy 创建者ID，用于记录谁创建了这个章节
     * @return 返回新添加的章节的ID
     */
    @Override
    public String addChapter(Chapter chapter, Long createBy) {
        String bookId = chapter.getBookId();
        // 检查章节关联的教材
        Book book = getBookById(bookId);
        if (book == null) {
            throw new IllegalArgumentException("教材不存在");
        }
        // 检查章节是否已经存在
        boolean existPo = chapterPOMapper.existsChapter(Collections.singletonList(chapter.getId()));
        if (existPo) {
            // 如果章节已存在，抛出异常
            throw new IllegalArgumentException("章节已存在");
        }
        ChapterPO existNumberPo = chapterPOMapper.selectByBookIdAndNumber(chapter.getBookId(), chapter.getChapterNumber());
        if (existNumberPo != null) {
            // 如果章节已存在，抛出异常
            throw new IllegalArgumentException("章节编号已存在");
        }
        // 创建新的章节持久化对象
        ChapterPO chapterPO = new ChapterPO();
        chapterPO.fromEntity(chapter);
        chapterPO.setCreateBy(createBy);
        // 插入章节到数据库
        int res = chapterPOMapper.insertSelective(chapterPO);
        if (res > 0) {
            // 记录操作日志
            operationLogService.log(bookId, createBy, MessageFormat.format("新增“{0}”", chapter.getName()), BookOperationEnum.INSERT.getCode());
            // 发布章节新增消息
            chapterEventPublisher.chapterEventPublisher(bookId, chapterPO.getId(), EventTypeEnum.ADD, createBy);
            return chapterPO.getId();
        }
        return null;
    }

    @Override
    public boolean editBook(Book book) {
        BookPO bookPO = new BookPO(book);
        int res = bookPOMapper.updateByPrimaryKeySelective(bookPO);
        // 更新教材基本信息
        BookBasicPO originaBookBasicPO = bookBasicPOMapper.selectByBookId(book.getId());
        BookBasicPO bookBasicPO = new BookBasicPO(originaBookBasicPO.getId(), book);
        int basicRes = bookBasicPOMapper.updateByPrimaryKeySelective(bookBasicPO);
        if (res > 0 && basicRes > 0) {
            // 记录操作日志
            operationLogService.log(bookPO.getId(), book.getUpdateBy(), "修改了基本信息", BookOperationEnum.UPDATE.getCode());
            // 发布教材编辑消息
            bookEventPublisher.bookEventPublisher(bookPO.getId(), EventTypeEnum.EDIT, book.getUpdateBy());
        }
        return true;
    }

    @Override
    public UserBookList getUserBooks(Long userId, BookSearchParam param, Long orgId, Integer topN) {
        List<UserBook> userBookList = getUserBookList(userId, param, orgId, PermissionTypeEnum.OWNER, PermissionTypeEnum.READ);
        if (userBookList.isEmpty()) {
            return new UserBookList(Collections.emptyList(), 0);
        }
        List<UserBook> sortedList = userBookList.stream().sorted(Comparator.comparing(
                s -> DateUtil.max(((UserBook) s).getEditTime(), ((UserBook) s).getJoinEditTime()),
                Comparator.nullsFirst(Comparator.naturalOrder())
        ).reversed()).limit(topN == null ? MAX_TOP_N : topN).toList();
        return new UserBookList(sortedList, userBookList.size());
    }

    @Override
    public UserBookList getMyLastVersionBookList(Long userId, LatestBookSearchParam param, Long orgId) {
        BookSearchParam bookSearchParam = new BookSearchParam();
        bookSearchParam.setBookName(param.getBookName());
        bookSearchParam.setSeriesId(param.getSeriesId());
        List<UserBook> userBookList = getUserBookList(userId, bookSearchParam, orgId, PermissionTypeEnum.OWNER, PermissionTypeEnum.READ);
        if (userBookList.isEmpty()) {
            return new UserBookList(Collections.emptyList(), 0);
        }

        List<String> bookIds = userBookList.stream().map(UserBook::getBookId).toList();
        List<BookVersionPO> bookLatestVersionList = bookVersionPOMapper.selectBookLatestBookVersionByBookIds(bookIds);
        if (CollectionUtils.isEmpty(bookLatestVersionList)) {
            return new UserBookList(Collections.emptyList(), 0);
        }

        Map<String, BookVersion> bookLatestVersionMap = bookLatestVersionList.stream()
                .collect(Collectors.toMap(BookVersionPO::getBookId, BookVersionPO::toEntity, (b1, b2) -> b1));
        List<UserBook> haveBookVersionList = new ArrayList<>();
        for (UserBook userBook : userBookList) {
            if (!bookLatestVersionMap.containsKey(userBook.getBookId())) {
                continue;
            }
            userBook.getBook().setBookVersion(bookLatestVersionMap.get(userBook.getBookId()));
            haveBookVersionList.add(userBook);
        }

        //没有版本信息的是绝对没有上架的教材
        if (CollectionUtils.isEmpty(haveBookVersionList)) {
            return new UserBookList(Collections.emptyList(), 0);
        }

        Integer totalCount = bookVersionPOMapper.selectBookLatestBookVersionCountByBookIds(bookIds);
        List<UserBook> sortedList = haveBookVersionList.stream().sorted(Comparator.comparing(
                s -> DateUtil.max(((UserBook) s).getBook().getBookVersion().getCreateTime(), ((UserBook) s).getBook().getBookVersion().getUpdateTime()),
                Comparator.nullsFirst(Comparator.naturalOrder())
        ).reversed()).skip(param.getPageParams().getOffset()).limit(param.getPageParams().getLimit()).toList();
        return new UserBookList(sortedList, totalCount);
    }

    @Override
    public UserBookList getUserPreviewBooks(Long userId, BookSearchParam param) {
        List<UserBook> userBookList = getUserBookList(userId, param, null, PermissionTypeEnum.SHARE);
        if (userBookList.isEmpty()) {
            return new UserBookList(Collections.emptyList(), 0);
        }
        List<UserBook> sortedList = userBookList.stream()
                .sorted(Comparator.comparing(UserBook::getJoinPreviewTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                .toList();
        return new UserBookList(sortedList, sortedList.size());
    }

    /**
     * 获取用户拥有的教材列表
     *
     * @param userId          用户ID
     * @param param           查询参数，包括教材名称和系列ID
     * @param orgId           机构ID
     * @param permissionTypes 权限类型数组
     * @return 用户拥有的教材列表
     * @throws IllegalArgumentException 如果用户ID或查询参数为空
     */
    private List<UserBook> getUserBookList(Long userId, BookSearchParam param, Long orgId, PermissionTypeEnum... permissionTypes) {
        // 检查用户ID是否为空
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        // 检查查询参数是否为空
        if (param == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        // 构建权限查询参数列表
        List<PermissionSearchParam> params = new ArrayList<>(permissionTypes.length);
        for (PermissionTypeEnum permissionType : permissionTypes) {
            params.add(new PermissionSearchParam(userId, ResourceTypeEnum.BOOK, permissionType));
        }

        // 获取用户拥有的资源ID列表
        List<String> userResources = resourcePermissionService.getPermissions(params).stream().map(ResourcePermission::getResourceId).toList();
        // 如果用户没有资源，则返回空列表
        if (userResources.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据机构ID和资源ID列表查询教材信息
        List<BookPO> books = bookPOMapper.selectByOrgIdAndIds(orgId, userResources);
        // 如果没有找到教材，则返回空列表
        if (books.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取教材ID列表
        List<String> bookIds = books.stream().map(BookPO::getId).toList();
        // 查询教材的基本信息
        Map<String, BookBasicPO> bookBasicMap = bookBasicPOMapper.selectByBookIdAndSeriesIdAndName(bookIds, param.getSeriesId(), param.getBookName())
                .stream().collect(Collectors.toMap(BookBasicPO::getBookId, b -> b, (b1, b2) -> b1));
        //根据条件没有查询到的数据，直接返回空列表
        if (CollectionUtils.isEmpty(bookBasicMap)){
            return Collections.emptyList();
        }

        // 查询用户对教材的操作活动信息
        Map<String, BookUserActivityInfoPO> bookUserActivityMap = bookUserActivityPOMapper.selectByUserIdAndBookIds(userId, bookIds).
                stream().collect(Collectors.toMap(BookUserActivityInfoPO::getBookId, b -> b, (b1, b2) -> b1));

        // 构建系列ID到教材ID的映射
        Map<String, Long> seriesIdMap = bookBasicMap.values().stream().collect(Collectors.toMap(BookBasicPO::getBookId, BookBasicPO::getSeriesId, (b1, b2) -> b1));

        // 查询系列信息
        Map<Long, Series> seriesMap = seriesService.batchGetSeriesByIds(seriesIdMap.values()).stream()
                .collect(Collectors.toMap(Series::getId, series -> series));

        //过滤应该返回的列表数据
        List<BookPO> shouldReturnBookList = books.stream().filter(b -> bookBasicMap.containsKey(b.getId())).collect(Collectors.toList());
        // 将查询结果转换为UserBook对象列表
        return BookUserActivityInfoPO.toUserBookEntities(shouldReturnBookList, userId, bookUserActivityMap, bookBasicMap, seriesMap);
    }

    @Override
    public Book getBookById(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return null;
        }
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookId);
        if (bookPO == null) {
            return null;
        }
        BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookId(bookId);
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByBookId(bookId);
        BookCopyrightPO bookCopyrightPO = copyrightPOMapper.selectByBookIdAndVersion(bookId, DEFAULT_VERSION_NUMBER);
        Book book = bookPO.toEntity();
        if (bookBasicPO != null) {
            Series series = seriesService.getSeriesById(bookBasicPO.getSeriesId());
            BookBasic entity = bookBasicPO.toEntity();
            entity.setSeries(series);
            book.fillBasicInfo(entity);
        }
        if (bookIntroPO != null) {
            book.fillBookIntro(bookIntroPO.toEntity());
        }
        if (bookCopyrightPO != null) {
            book.fillBookCopyright(bookCopyrightPO.toEntity());
        }
        return book;
    }

    /**
     * 根据教材id获取教材封面和简介信息
     *
     * @param bookId 教材ID，用于查询教材和简介信息
     * @return 返回一个Book对象
     */
    @Override
    public Book getBookCoverIntro(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookId);
        if (bookPO == null) {
            return null;
        }
        Book book = bookPO.toEntity();
        BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookId(bookId);
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByBookId(bookId);
        if (bookBasicPO != null) {
            book.fillBasicInfo(bookBasicPO.toEntity());
        }
        if (bookIntroPO != null) {
            book.fillBookIntro(bookIntroPO.toEntity());
        }
        return book;
    }

    /**
     * 编辑教材封面和简介信息
     *
     * @param userId 用户ID
     * @param book   教材封面和简介参数
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editBookCoverIntro(Long userId, Book book) {
        if (StringUtils.isBlank(book.getId())) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(book.getId());
        if (bookPO == null) {
            throw new IllegalArgumentException("教材不存在");
        }
        // 更新教材信息
        bookPOMapper.updateTimeByBookId(book.getId());
        // 更新教材封面信息
        BookBasicPO originaBookBasicPO = bookBasicPOMapper.selectByBookId(book.getId());
        BookBasicPO bookBasicPO = new BookBasicPO(originaBookBasicPO.getId(), book);
        bookBasicPOMapper.updateByPrimaryKeySelective(bookBasicPO);
        // 新增或更新教材简介信息
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByBookId(book.getId());
        if (bookIntroPO == null) {
            BookIntroPO saveBookIntroPO = new BookIntroPO();
            saveBookIntroPO.setBookId(book.getId());
            saveBookIntroPO.setDescription(book.getDescription());
            saveBookIntroPO.setCreateBy(userId);
            bookIntroPOMapper.insertSelective(saveBookIntroPO);
            // 记录操作日志
            operationLogService.log(book.getId(), userId, "新增了教材简介", BookOperationEnum.INSERT.getCode());
        } else {
            BookIntroPO updateBookIntroPO = new BookIntroPO();
            updateBookIntroPO.setId(bookIntroPO.getId());
            updateBookIntroPO.setDescription(book.getDescription());
            updateBookIntroPO.setUpdateBy(userId);
            bookIntroPOMapper.updateByPrimaryKeySelective(updateBookIntroPO);
            // 记录操作日志
            operationLogService.log(book.getId(), userId, "修改了教材简介", BookOperationEnum.UPDATE.getCode());
        }
        // 发布教材变更消息
        bookEventPublisher.bookEventPublisher(bookPO.getId(), EventTypeEnum.EDIT, userId);
        return true;
    }

    @Override
    public Map<String, List<ResourceUser>> getBookCollaborators(List<String> bookIds, Long orgId) {
        if (CollectionUtils.isEmpty(bookIds)) {
            return Collections.emptyMap();
        }
        List<PermissionSearchParam> params = bookIds.stream().map(bookId -> new PermissionSearchParam(bookId, ResourceTypeEnum.BOOK, PermissionTypeEnum.READ))
                .toList();
        return getResourceUserMap(params, orgId);
    }

    @Override
    public Map<String, List<ResourceUser>> getChapterCollaborators(List<String> chapterIds, Long orgId) {
        if (CollectionUtils.isEmpty(chapterIds)) {
            return Collections.emptyMap();
        }
        List<PermissionSearchParam> params = chapterIds.stream().map(chapterId -> new PermissionSearchParam(chapterId, ResourceTypeEnum.CHAPTER, PermissionTypeEnum.EDIT))
                .toList();
        return getResourceUserMap(params, orgId);
    }

    /**
     * 根据章节编号对指定书籍的章节列表进行排序。
     *
     * @param bookId      书籍ID，用于验证章节列表中的每个章节是否属于该书籍。
     * @param chapterList 待排序的章节列表。
     * @param updateBy    执行更新操作的用户ID，用于记录最后更新者信息。
     * @return 如果所有章节都成功排序且属于给定的书籍，则返回true；否则返回false。
     */
    @Override
    public Boolean sortChapterListInBook(String bookId, List<Chapter> chapterList, Long updateBy) {
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookId);
        if (bookPO == null) {
            throw new IllegalArgumentException("书籍不存在");
        }
        if (Boolean.FALSE.equals(bookPO.getEnable())) {
            throw new IllegalArgumentException("书籍已删除");
        }
        // 获取当前书籍的章节列表
        List<ChapterPO> chapterPOList = chapterPOMapper.selectByBookId(bookId);
        if (CollectionUtils.isEmpty(chapterPOList)) {
            throw new IllegalArgumentException("书籍章节列表为空,无法排序");
        }
        if (chapterList.size() != chapterPOList.size()) {
            throw new IllegalArgumentException("排序章节列表数量与书籍中章节数量不一致");
        }
        //对比原数据库中的章节列表与要排序的章节列表是否一致
        if (!compareChapterLists(chapterList, chapterPOList)) {
            throw new IllegalArgumentException("章节列表与数据库中的章节列表不一致");
        }
        if (chapterList.stream().map(Chapter::getChapterNumber).distinct().count() != chapterList.size()) {
            throw new IllegalArgumentException("章节列表中存在重复章节序号");
        }
        List<ChapterPO> updatedChapterPOList = new ArrayList<>();
        for (Chapter chapter : chapterList) {
            ChapterPO updatedChapterPO = new ChapterPO();
            updatedChapterPO.setId(chapter.getId());
            updatedChapterPO.setChapterNumber(chapter.getChapterNumber());
            updatedChapterPO.setUpdateBy(updateBy);
            updatedChapterPOList.add(updatedChapterPO);
        }
        int rows = chapterPOMapper.batchUpdateChapterNumber(updatedChapterPOList);
        if (rows <= 0) {
            throw new IllegalArgumentException("章节排序失败");
        }
        return true;
    }

    @Override
    public Long getEditorId(String bookId) {
        return bookPOMapper.selectEditorId(bookId);
    }

    @Override
    public List<String> getHasEditChapterIdsByBookId(Long userId, String bookId) {
        Long editorId = getEditorId(bookId);
        if (userId.equals(editorId)) {
            return chapterPOMapper.selectIdsByBookId(bookId);
        }
        List<String> chapterIds = resourcePermissionService.getUserResources(userId, ResourceTypeEnum.CHAPTER, PermissionTypeEnum.EDIT);
        if (CollectionUtils.isEmpty(chapterIds)) {
            return Collections.emptyList();
        }
        return chapterPOMapper.selectIdsByChapterIds(chapterIds);
    }

    /**
     * 对比要排序的章节列表与数据库中的章节列表是否一致
     *
     * @param sourceList 要排序的章节列表
     * @param dbList     数据库中的章节列表
     * @return 如果两个列表包含相同的章节则返回true，否则返回false
     */
    private boolean compareChapterLists(List<Chapter> sourceList, List<ChapterPO> dbList) {
        // 转换为Set便于比较，使用章节ID作为唯一标识
        Set<String> sourceChapterIds = sourceList.stream()
                .map(Chapter::getId)
                .collect(Collectors.toSet());

        Set<String> dbChapterIds = dbList.stream()
                .map(ChapterPO::getId)
                .collect(Collectors.toSet());

        // 检查两个集合是否完全相等
        return sourceChapterIds.equals(dbChapterIds);
    }

    private Map<String, List<ResourceUser>> getResourceUserMap(List<PermissionSearchParam> params, Long orgId) {
        List<ResourcePermission> permissions = resourcePermissionService.getPermissions(params);
        if (CollectionUtils.isEmpty(permissions)) {
            return Collections.emptyMap();
        }
        List<Long> userIds = permissions.stream().map(ResourcePermission::getUserId).toList();
        Map<Long, UserInfo> userMap = userService.getUserMap(userIds);
        Set<Long> validityUserIds = userService.checkUserValidityWithUserIdsAndOrgId(orgId, userIds);
        return permissions.stream()
                .sorted(Comparator.comparing(ResourcePermission::getUpdateTime))
                .collect(Collectors.groupingBy(ResourcePermission::getResourceId,
                        Collectors.mapping(p -> {
                            UserInfo userInfo = userMap.get(p.getUserId());
                            if (userInfo == null) {
                                return null;
                            }
                            return new ResourceUser(p.getUserId(), userInfo.getName(), userInfo.getCellPhone(), validityUserIds.contains(p.getUserId()));
                        }, Collectors.filtering(Objects::nonNull,
                                Collectors.toCollection(ArrayList::new)))));
    }

    /**
     * 根据教材名称获取教材列表
     *
     * @param bookName 教材名称
     * @return 教材列表
     */
    @Override
    public List<Book> getByBookName(String bookName) {
        List<BookBasicPO> bookBasicPOList = bookBasicPOMapper.selectByBookName(bookName);
        List<String> bookIds = bookBasicPOList.stream().map(BookBasicPO::getBookId).distinct().toList();
        if (CollectionUtils.isEmpty(bookIds)) {
            return Collections.emptyList();
        }
        Map<String, BookPO> bookPOMap = bookPOMapper.selectByIds(bookIds).stream()
                .collect(Collectors.toMap(BookPO::getId, b -> b, (b1, b2) -> b1));
        return bookBasicPOList.stream().map(bookBasicPO -> {
            BookPO bookPO = bookPOMap.get(bookBasicPO.getBookId());
            if (bookPO == null) {
                return null;
            }
            Book book = bookPO.toEntity();
            book.fillBasicInfo(bookBasicPO.toEntity());
            return book;
        }).filter(Objects::nonNull).toList();
    }

    /**
     * 根据教材ID获取教材基本信息
     *
     * @param bookId 教材ID
     * @return 教材基本信息
     */
    @Override
    public BookBasic getBookBasicByBookId(String bookId) {
        BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookIdAndVersion(bookId, DEFAULT_VERSION_NUMBER);
        if (bookBasicPO == null) {
            return null;
        }
        return bookBasicPO.toEntity().generateMd5();
    }

    /**
     * 生成教材基本信息版本
     *
     * @param bookId 教材id
     * @return BookBasic基本信息版本
     */
    @Override
    public BookBasic generateBookBasicVersion(String bookId) {
        String version = IdentifierUtil.generateVersion();
        bookBasicPOMapper.generateVersion(bookId, version);
        BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookIdAndVersion(bookId, version);
        if (bookBasicPO == null) {
            return null;
        }
        return bookBasicPO.toEntity().generateMd5();
    }

    /**
     * 根据教材ID获取教材简介
     *
     * @param bookId 教材ID
     * @return 教材简介
     */
    @Override
    public BookIntro getBookIntroByBookId(String bookId) {
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByBookIdAndVersion(bookId, DEFAULT_VERSION_NUMBER);
        if (bookIntroPO == null) {
            return null;
        }
        return bookIntroPO.toEntity().generateMd5();
    }

    /**
     * 生成教材简介版本
     *
     * @param bookId 教材ID
     * @return 简介
     */
    @Override
    public BookIntro generateBookIntroVersion(String bookId) {
        String version = IdentifierUtil.generateVersion();
        bookIntroPOMapper.generateVersion(bookId, version);
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByBookIdAndVersion(bookId, version);
        if (bookIntroPO == null) {
            return null;
        }
        return bookIntroPO.toEntity().generateMd5();
    }

    @Override
    public boolean editBookCopyright(BookCopyright bookCopyright) {
        if (bookCopyright == null) {
            throw new IllegalArgumentException("bookCopyright is null");
        }
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookCopyright.getBookId());
        if (bookPO == null) {
            throw new IllegalArgumentException("bookPO is null");
        }
        BookCopyrightPO bookCopyrightPO = new BookCopyrightPO(bookCopyright);
        BookCopyrightPO existingBookCopyrightPO = copyrightPOMapper.selectByBookIdAndVersion(bookCopyright.getBookId(), DEFAULT_VERSION_NUMBER);
        if (existingBookCopyrightPO != null) {
            bookCopyrightPO.setId(existingBookCopyrightPO.getId());
            copyrightPOMapper.updateByPrimaryKeySelective(bookCopyrightPO);
        } else {
            copyrightPOMapper.insertSelective(bookCopyrightPO);
        }
        return false;
    }

    @Override
    public BookCopyright getBookCopyright(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            throw new IllegalArgumentException("bookId is blank");
        }
        BookCopyrightPO bookCopyrightPO = copyrightPOMapper.selectByBookIdAndVersion(bookId, DEFAULT_VERSION_NUMBER);
        if (bookCopyrightPO == null) {
            return null;
        }
        return bookCopyrightPO.toEntity();
    }

    /**
     * 根据简介ID，获取简介
     *
     * @param introId 简介ID
     * @return 简介
     */
    @Override
    public BookIntro getBookIntroById(Long introId) {
        if (introId == null) {
            throw new IllegalArgumentException("introId is null");
        }
        BookIntroPO bookIntroPO = bookIntroPOMapper.selectByPrimaryKey(introId);
        if (bookIntroPO != null && Boolean.TRUE.equals(bookIntroPO.getEnable())) {
            return bookIntroPO.toEntity();
        }
        return null;
    }

    /**
     * 根据基本信息ID，获取基本信息
     *
     * @param basicInfoId 基础信息ID
     * @return 基本信息
     */
    @Override
    public BookBasic getBookBasicInfoById(Long basicInfoId) {
        if (basicInfoId == null) {
            throw new IllegalArgumentException("basicInfoId is null");
        }
        BookBasicPO bookBasicPO = bookBasicPOMapper.selectByPrimaryKey(basicInfoId);
        if (bookBasicPO != null && Boolean.TRUE.equals(bookBasicPO.getEnable())) {
            return bookBasicPO.toEntity();
        }
        return null;
    }


    /**
     * 根据版权ID，获取版权信息
     *
     * @param copyrightId 版权ID
     * @return 版权信息
     */
    @Override
    public BookCopyright getBookCopyrightById(Long copyrightId) {
        if (copyrightId == null) {
            throw new IllegalArgumentException("copyrightId is null");
        }
        BookCopyrightPO bookCopyrightPO = copyrightPOMapper.selectByPrimaryKey(copyrightId);
        if (bookCopyrightPO != null && Boolean.TRUE.equals(bookCopyrightPO.getEnable())) {
            return bookCopyrightPO.toEntity();
        }
        return null;
    }

    //todo 一下三个save方法 考虑是否要把审计信息（create、update信息）从实体中取出来

    /**
     * 保存教材基本信息
     *
     * @param bookBasic 教材基本信息
     * @return 保存结果
     */
    @Override
    public Long saveBookBasic(BookBasic bookBasic) {
        BookBasicPO basicPO = new BookBasicPO();
        basicPO = basicPO.fromEntity(bookBasic);
        if (basicPO.getId() == null) {
            int res = bookBasicPOMapper.insertSelective(basicPO);
            if (res > 0) {
                return basicPO.getId();
            }
        } else {
            basicPO.setUpdateTime(new Date());
            basicPO.setUpdateBy(bookBasic.getCreateBy());
            int res = bookBasicPOMapper.updateByPrimaryKeySelective(basicPO);
            if (res > 0) {
                return basicPO.getId();
            }
        }
        return null;
    }

    /**
     * 保存教材简介
     *
     * @param bookIntro 教材简介
     * @return 保存结果
     */
    @Override
    public Long saveBookIntro(BookIntro bookIntro) {
        BookIntroPO introPO = new BookIntroPO();
        introPO = introPO.fromEntity(bookIntro);
        int res;
        if (introPO.getId() == null) {
            res = bookIntroPOMapper.insertSelective(introPO);
        } else {
            res = bookIntroPOMapper.updateByPrimaryKeySelective(introPO);
        }
        if (res > 0) {
            return introPO.getId();
        }
        return null;
    }

    /**
     * 保存教材版权信息
     *
     * @param copyright 版权信息
     * @return 新版权信息的id
     */
    @Override
    public Long saveBookCopyright(BookCopyright copyright) {
        BookCopyrightPO copyrightPO = new BookCopyrightPO();
        copyrightPO = copyrightPO.fromEntity(copyright);
        int res;
        if (copyrightPO.getId() == null) {
            res = copyrightPOMapper.insertSelective(copyrightPO);
        } else {
            res = copyrightPOMapper.updateByPrimaryKeySelective(copyrightPO);
        }
        if (res > 0) {
            return copyrightPO.getId();
        }
        return null;
    }

    @Override
    public Book getBookByBookIdAndVersion(String bookId, String versionNum) {
        if (StringUtils.isBlank(versionNum)) {
            BookVersionPO versionPO = bookVersionPOMapper.selectLatestPublishedBookVersionByBookId(bookId);
            if (versionPO == null) {
                throw new NoSuchElementException("教材未发布，请检查请求参数");
            }
            versionNum = versionPO.getVersionNum();
        }
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectByBookIdAndVersionNum(bookId, versionNum);
        if (bookVersionPO == null) {
            throw new NoSuchElementException("bookVersionPO is null");
        }
        Book book = buildBook(bookVersionPO.toEntity());
        log.debug("getBookByBookIdAndVersion: Book{}", book);
        return book;
    }


    /**
     * 根据版本ID获取对应的书籍信息
     *
     * @param versionId 书籍版本ID
     * @return 对应的书籍对象
     * @throws NoSuchElementException 当书籍版本不存在或未启用时抛出此异常
     */
    @Override
    public Book getBookByVersionId(Long versionId) {
        // 根据版本ID查询书籍版本持久化对象
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectById(versionId);
        // 检查书籍版本是否存在且已启用
        if (bookVersionPO == null || !bookVersionPO.getEnable()) {
            throw new NoSuchElementException("bookVersionPO is null");
        }
        // 将书籍版本持久化对象转换为实体对象并构建书籍对象
        Book book = buildBook(bookVersionPO.toEntity());
        // 记录日志输出书籍信息
        log.debug("book:{}", book);
        return book;
    }

    /**
     * 获取教材版本下的变更内容（章节、基础信息、版权等）
     *
     * @param bookVersionId 教材版本ID
     * @return 变更内容列表
     */
    @Override
    public List<BookChangeItemEntity> getBookChangeListByVersionId(Long bookVersionId) {
        BookPublishPackagePO bookPublishPackagePO = bookPublishPackagePOMapper.selectByBookVersionId(bookVersionId);
        if (bookPublishPackagePO != null && !CollectionUtils.isEmpty((bookPublishPackagePO.getPublishPackage()))) {
            return bookPublishPackagePO.getPublishPackage().stream().map(BookPublishItem::toEntity).toList();
        }
        return Collections.emptyList();
    }

    @Override
    public Book getBookBasicInfoByIdAndVersion(String bookId, String versionNum) {
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectByBookIdAndVersionNum(bookId, versionNum);
        if (bookVersionPO == null) {
            throw new NoSuchElementException("bookVersionPO is null");
        }
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookVersionPO.getBookId());
        if (bookPO == null || Boolean.FALSE.equals(bookPO.getEnable())) {
            throw new NoSuchElementException("book is null");
        }
        Book book = bookPO.toEntity();
        book.setBookVersion(bookVersionPO.toEntity());
        BookBasicPO bookBasic = bookBasicPOMapper.selectByBookVersion(bookVersionPO.getId());
        if (bookBasic != null) {
            BookBasic bookBasicEntity = bookBasic.toEntity();
            bookBasicEntity.setSeries(seriesService.getSeriesById(bookBasicEntity.getSeriesId()));
            book.fillBasicInfo(bookBasicEntity);
        }
        return book;
    }


    /**
     * 根据图书版本构建图书对象
     * 此方法首先通过图书版本中的图书ID获取图书持久化对象（BookPO），并进行有效性检查
     * 然后，它将BookPO转换为图书实体对象（Book），并设置当前版本信息
     * 接着依次获取并填充图书的基本信息、简介、版权信息以及章节列表
     *
     * @param bookVersion 图书版本对象，包含构建图书所需的版本信息
     * @return 返回一个完整填充了相关信息的图书对象
     * @throws NoSuchElementException 如果图书不存在或未启用，抛出此异常
     */
    private Book buildBook(BookVersion bookVersion) {
        BookPO bookPO = bookPOMapper.selectByPrimaryKey(bookVersion.getBookId());
        if (bookPO == null) {
            throw new NoSuchElementException("book is null");
        }
        if (Boolean.FALSE.equals(bookPO.getEnable())) {
            throw new NoSuchElementException("book is disable");
        }
        Book book = bookPO.toEntity();
        book.setBookVersion(bookVersion);
        // 获取教材基本信息
        BookBasicPO bookBasic = bookBasicPOMapper.selectByBookVersion(bookVersion.getId());
        if (bookBasic != null) {
            BookBasic bookBasicEntity = bookBasic.toEntity();
            bookBasicEntity.setSeries(seriesService.getSeriesById(bookBasicEntity.getSeriesId()));
            book.fillBasicInfo(bookBasicEntity);
        }
        // 获取教材简介
        BookIntroPO bookIntro = bookIntroPOMapper.selectByBookVersion(bookVersion.getId());
        if (bookIntro != null) {
            BookIntro bookIntroEntity = bookIntro.toEntity();
            book.fillBookIntro(bookIntroEntity);
        }

        // 获取教材版权信息
        BookCopyrightPO bookCopyright = copyrightPOMapper.selectByBookVersion(bookVersion.getId());
        if (bookCopyright != null) {
            BookCopyright bookCopyrightEntity = bookCopyright.toEntity();
            book.fillBookCopyright(bookCopyrightEntity);
        }
        // 处理配套资源
        List<ComplementResource> complementResourceList = complementResourceService.getResourceByBookVersionId(bookVersion.getId());
        if (!CollectionUtils.isEmpty(complementResourceList)) {
            book.fillComplementResourceList(complementResourceList);
        }

        // 处理教材下的卷子版本数据
        List<PaperSyncInfo> paperSyncInfos = paperVersionService.getPaperSyncInfoByBookVersionId(bookVersion.getId());
        if(!CollectionUtils.isEmpty(paperSyncInfos)) {
            book.setPaperSyncInfos(paperSyncInfos);
        }

        //处理教材下的章节信息
        List<Chapter> chapterList = chapterService.getChapterListByBookVersionId(bookVersion.getId());
        book.setChapterList(chapterList);

        return book;
    }

    /**
     * 批量获取教材已上架的版本信息
     *
     * @param bookIds 教材 ID 列表
     * @return 教材 ID 和最新已上架版本信息的 Map
     */
    private Map<String, BookVersionPO> getBookIdAndLatestPublishedVersionMap(List<String> bookIds) {
        // 构建教材 ID 和最新已上架版本信息的 Map
        Map<String, BookVersionPO> bookIdAndVersionMap = Maps.newHashMap();

        // 批量获取教材已上架的版本信息
        List<BookVersionPO> bookVersions = bookVersionPOMapper.selectListByBookIds(bookIds);

        bookVersions.forEach(version -> {
            if (bookIdAndVersionMap.containsKey(version.getBookId())) {
                BookVersionPO prevVersion = bookIdAndVersionMap.get(version.getBookId());
                if (version.getVersionNum().compareTo(prevVersion.getVersionNum()) > 0) {
                    bookIdAndVersionMap.put(version.getBookId(), version);
                }
            } else {
                bookIdAndVersionMap.put(version.getBookId(), version);
            }
        });

        return bookIdAndVersionMap;
    }

    /**
     * 准备教材简单数据 DTO
     */
    private BookSimpleDataDTO prepareBookSimpleDataDTO(String bookId, BookVersionPO version, BookBasicPO basic,
                                                       BookIntroPO intro, BookCopyrightPO copyright) {
        BookSimpleDataDTO data = new BookSimpleDataDTO();
        data.setBookId(bookId);
        data.setVersionNumber(version.getVersionNum());

        // 设置基本信息
        if (Objects.nonNull(basic)) {
            data.setBasic(new BookBasicDTO(basic.toEntity()));
        }

        // 设置简介
        if (Objects.nonNull(intro)) {
            data.setIntro(new BookIntroDTO(intro.toEntity()));
        }

        // 设置版权信息
        if (Objects.nonNull(copyright)) {
            data.setCopyright(new BookCopyrightDTO(copyright.toEntity()));
        }

        return data;
    }

    /**
     * 获取全部书籍的简要数据
     *
     * @param tenantId 租户 ID
     */
    @Override
    public List<BookSimpleDataDTO> getBookSimpleDataList(long tenantId) {
        // 查询当前租户的所有教材 ID 列表
//        List<String> bookIds = bookPOMapper.selectBookIdsByOrgIds(Lists.newArrayList(101L, 150L));
        List<String> bookIds = bookPOMapper.selectIdsByTenantId(tenantId);
        if (CollectionUtils.isEmpty(bookIds)) {
            return Collections.emptyList();
        }

        // 构建教材 ID 和最新已上架版本号的 Map
        Map<String, BookVersionPO> bookIdAndVersionMap = getBookIdAndLatestPublishedVersionMap(bookIds);
        if (CollectionUtils.isEmpty(bookIdAndVersionMap)) {
            return Collections.emptyList();
        }

        // 校正教材 ID 列表
        bookIds = bookIds.stream()
                .filter(bookIdAndVersionMap::containsKey)
                .collect(Collectors.toList());

        // 批量获取教材基本信息
        List<Long> bookVersionIds = bookIdAndVersionMap.values()
                .stream()
                .map(BookVersionPO::getId)
                .toList();
        List<BookBasicPO> basicInfos = bookBasicPOMapper.selectListByBookVersionIds(bookVersionIds);
        Map<String, BookBasicPO> bookIdAndBasicMap = basicInfos.stream()
                .collect(Collectors.toMap(BookBasicPO::getBookId, basicInfo -> basicInfo));

        // 批量获取教材简介
        List<BookIntroPO> intros = bookIntroPOMapper.selectListByBookVersionIds(bookVersionIds);
        Map<String, BookIntroPO> bookIdAndIntroMap = intros.stream()
                .collect(Collectors.toMap(BookIntroPO::getBookId, intro -> intro));

        // 批量获取教材版权信息
        List<BookCopyrightPO> copyrights = copyrightPOMapper.selectListByBookVersionIds(bookVersionIds);
        Map<String, BookCopyrightPO> bookIdAndCopyrightMap = copyrights.stream()
                .collect(Collectors.toMap(BookCopyrightPO::getBookId, copyright -> copyright));

        // 遍历教材 ID 列表，构建 BookSimpleDataDTO 对象
        return bookIds.stream()
                .map(bookId -> prepareBookSimpleDataDTO(bookId, bookIdAndVersionMap.get(bookId),
                        bookIdAndBasicMap.get(bookId), bookIdAndIntroMap.get(bookId),
                        bookIdAndCopyrightMap.get(bookId)))
                .collect(Collectors.toList());
    }

}
