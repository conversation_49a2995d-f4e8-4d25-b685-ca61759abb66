package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.PaperBookRelationPOMapper;
import com.unipus.digitalbook.dao.PaperChapterReferencePOMapper;
import com.unipus.digitalbook.dao.PaperChapterReferenceVersionPOMapper;
import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.po.paper.PaperBookRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperChapterReferencePO;
import com.unipus.digitalbook.model.po.paper.PaperChapterReferenceVersionPO;
import com.unipus.digitalbook.service.PaperReferenceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 试卷引用服务实现类
 */
@Service
@Slf4j
public class PaperReferenceServiceImpl implements PaperReferenceService {

    @Resource
    private PaperBookRelationPOMapper paperBookRelationPOMapper;
    @Resource
    private PaperChapterReferencePOMapper paperChapterReferencePOMapper;
    @Resource
    private PaperChapterReferenceVersionPOMapper paperChapterReferenceVersionPOMapper;

    /**
     * 批量保存教材中试卷引用
     * -- 检测引用关系是否存在，存在则更新引用关系，不存在则添加引用关系
     * -- 检查试卷是否存在，不存在则不允许添加引用，抛出异常
     * @param paperReferences 试卷引用对象列表(paperId,bookId,chapterId,position)
     * @param userId 当前用户ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePaperReference(List<PaperReference> paperReferences, Long userId) {
        if (CollectionUtils.isEmpty(paperReferences)) {
            log.debug("试卷引用列表为空");
            return;
        }

        String bookId = paperReferences.getFirst().getBookId();
        String chapterId = paperReferences.getFirst().getChapterId();

        // 1. 检查试卷是否有效
        checkPaperValid(bookId, chapterId, paperReferences);

        // 2. 去重处理新数据，相同key保留第一个
        Map<String, PaperReference> newReferenceMap = new LinkedHashMap<>();
        for (PaperReference pr : paperReferences) {
            String key = pr.getUniqueKey();
            if (newReferenceMap.containsKey(key)) {
                log.warn("发现重复的试卷引用：paperId={}, bookId={}, chapterId={}",pr.getPaperId(), pr.getBookId(), pr.getChapterId());
                continue;
            }
            newReferenceMap.put(key, pr);
        }

        // 3. 查询章节中已存在的引用关系
        Map<String, List<PaperChapterReferencePO>> groupedExistMap = getExistReferenceListMap(bookId, chapterId);

        // 4. 默认删除所有历史数据
        List<PaperChapterReferencePO> updateList = new ArrayList<>();
        groupedExistMap.values().stream().flatMap(List::stream).
            forEach(existingPO -> {
                existingPO.setUpdateBy(userId);
                existingPO.setEnable(false);
                updateList.add(existingPO);
            });

        List<PaperChapterReferencePO> insertList = new ArrayList<>();
        // 5. 处理新增或更新的数据
        for (PaperReference newReference : newReferenceMap.values()) {
            String key = newReference.getUniqueKey();

            if (groupedExistMap.containsKey(key)) {
                // 更新已有记录(取得最新的一条作为更新对象)
                // 所有历史对象已在updateList中存在，更新对象属性即可(更新引用位置，恢复为启用状态)
                PaperChapterReferencePO existingPO = groupedExistMap.get(key).getFirst();
                existingPO.setPosition(newReference.getPosition());
                existingPO.setUpdateBy(userId);
                existingPO.setEnable(true);
            } else {
                // 新增记录
                PaperChapterReferencePO po = new PaperChapterReferencePO(newReference, userId);
                po.setCreateBy(userId);
                po.setEnable(true);
                insertList.add(po);
            }
        }

        // 6. 执行批量保存
        if (!insertList.isEmpty()) {
            paperChapterReferencePOMapper.batchInsert(insertList);
            log.debug("批量插入试卷引用成功，共处理{}条记录", insertList.size());
        }
        if (!updateList.isEmpty()) {
            paperChapterReferencePOMapper.batchUpsert(updateList);
            log.debug("批量更新试卷引用成功，共处理{}条记录", updateList.size());
        }
    }

    // 查询章节中已存在的引用关系
    private Map<String, List<PaperChapterReferencePO>> getExistReferenceListMap(String bookId, String chapterId) {
        PaperChapterReferencePO queryPO = new PaperChapterReferencePO();
        queryPO.setBookId(bookId);
        queryPO.setChapterId(chapterId);
        List<PaperChapterReferencePO> existList = paperChapterReferencePOMapper.selectList(queryPO);
        if(CollectionUtils.isEmpty(existList)){
            return Map.of();
        }

        // 构建现有引用映射，按key分组并保留最新记录（按创建时间倒序排列）
        return existList.stream()
                .sorted(Comparator.comparing(PaperChapterReferencePO::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.groupingBy(PaperChapterReferencePO::getUniqueKey));
    }

    // 检查试卷有效性
    private void checkPaperValid(String bookId, String chapterId, List<PaperReference> paperReferences) {
        Set<String> paperIds = paperReferences.stream().map(PaperReference::getPaperId).collect(Collectors.toSet());

        // 校验试卷是否属于当前教材
        List<PaperBookRelationPO> validPapers = paperBookRelationPOMapper.selectList(bookId);
        if (CollectionUtils.isEmpty(validPapers)) {
            throw new BizException("教材中未找到任何试卷");
        }
        Set<String> validPaperIds = validPapers.stream().map(PaperBookRelationPO::getPaperId).collect(Collectors.toSet());
        Set<String> invalidPaperIds = paperIds.stream().filter(id -> !validPaperIds.contains(id)).collect(Collectors.toSet());
        if (!invalidPaperIds.isEmpty()) {
            throw new BizException("存在无效的试卷ID：" + invalidPaperIds + "，这些试卷不属于当前教材");
        }

        // 检测试卷是否已被其他章节引用
        List<PaperChapterReferencePO> references = paperChapterReferencePOMapper.selectReferenceByPaperIds(paperIds.stream().toList());
        if (!references.isEmpty()) {
            List<PaperChapterReferencePO> otherRefs = references.stream()
                .filter(po -> !po.getChapterId().equals(chapterId))
                .toList();
            if (!otherRefs.isEmpty()) {
                List<String> beUsedPaperNames = otherRefs.stream().map(PaperChapterReferencePO::getPaperName).toList();
                String paperNames = String.join("；", beUsedPaperNames);
                String message = MessageFormat.format("保存失败，当前章节引用的【{0}】已被当前教材使用，请删除后重新保存。", paperNames);
                log.warn(message);
                throw new BizException(message);
            }
        }
    }

    /**
     * 删除指定教材章节中的所有编辑态试卷引用
     * @param bookId 教材ID
     * @param chapterId 章节ID
     * @param userId 当前用户ID
     */
    @Override
    public void removePaperReferenceInChapter(String bookId, String chapterId, Long userId){
        paperChapterReferencePOMapper.removeReferenceInChapter(bookId, chapterId, userId);
    }

    /**
     * 添加教材中试卷引用
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    @Override
    public Boolean insertPaperReference(PaperReference paperReference, Long userId){
        PaperChapterReferencePO po = new PaperChapterReferencePO(paperReference, userId);
        return paperChapterReferencePOMapper.insert(po)>0;
    }

    /**
     * 删除教材中试卷引用
     * 仅仅删除编辑中的引用关系(引用关系的版本为默认版本)
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    @Override
    public Boolean deletePaperReference(PaperReference paperReference, Long userId){
        String paperId = paperReference.getPaperId();
        // 检查试卷是否存在发布中的版本
        PaperChapterReferencePO paperChapterReferencePO = new PaperChapterReferencePO(paperReference, userId);
        List<PaperChapterReferencePO> paperChapterReferencePOs = paperChapterReferencePOMapper.selectList(paperChapterReferencePO);
        if(CollectionUtils.isEmpty(paperChapterReferencePOs)){
            log.debug("试卷引用关系不存在，paperId：{}", paperId);
            throw new BizException("试卷引用关系不存在");
        }

        // 删除试卷与章节的引用关系
        return paperChapterReferencePOMapper.delete(List.of(paperChapterReferencePOs.getFirst().getId()), userId)>0;
    }

    /**
     * 保存试卷与特定章节版本的关系
     *
     * @param chapterId        章节ID
     * @param versionChapterId 版本章节ID
     * @param currentUserId    当前用户ID
     */
    @Override
    public void savePaperReferenceWithChapterVersion(String chapterId, Long versionChapterId, Long currentUserId) {
        // 查询章节引用的试卷
        PaperChapterReferencePO queryPO = new PaperChapterReferencePO();
        queryPO.setChapterId(chapterId);
        List<PaperChapterReferencePO> referenceList = paperChapterReferencePOMapper.selectList(queryPO);
        if (CollectionUtils.isEmpty(referenceList)) {
            log.debug("章节不存在引用关系，chapterId：{}", chapterId);
            return;
        }

        // 去重处理：保留每个paperId/bookId/chapterId组合中最新创建的记录
        Map<String, PaperChapterReferencePO> latestReferenceMap = new HashMap<>();
        for (PaperChapterReferencePO po : referenceList) {
            String key = po.getPaperId() + "_" + po.getBookId() + "_" + po.getChapterId();
            if (!latestReferenceMap.containsKey(key) || po.getCreateTime().after(latestReferenceMap.get(key).getCreateTime())) {
                latestReferenceMap.put(key, po);
            }
        }

        // 构建版本引用列表并批量插入
        List<PaperChapterReferenceVersionPO> versionReferenceList = latestReferenceMap.values().stream()
                .map(rpo -> new PaperChapterReferenceVersionPO(
                        rpo.getPaperId(),
                        rpo.getBookId(),
                        rpo.getChapterId(),
                        versionChapterId,
                        rpo.getPosition(),
                        currentUserId))
                .toList();
        if (!versionReferenceList.isEmpty()) {
            paperChapterReferenceVersionPOMapper.batchInsert(versionReferenceList);
        }
    }

    /**
     * 检查试卷引用是否存在
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷IDj集合
     */
    @Override
    public Set<String> checkPaperReferenceExist(List<String> paperIds) {
        if(CollectionUtils.isEmpty(paperIds)){
            return Set.of();
        }
        List<PaperChapterReferencePO> refList = paperChapterReferencePOMapper.selectReferenceByPaperIds(paperIds);
        if(CollectionUtils.isEmpty(refList)){
            log.debug("测试被教材章节使用:{}", paperIds);
            return Set.of();
        }
        return refList.stream().map(PaperChapterReferencePO::getPaperId).collect(Collectors.toSet());
    }

    /**
     * 检查试卷引用是否已发布
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷IDj集合
     */
    @Override
    public Set<String> getVersionedPaperReference(List<String> paperIds) {
        if(CollectionUtils.isEmpty(paperIds)){
            return Set.of();
        }
        List<PaperChapterReferenceVersionPO> refList = paperChapterReferenceVersionPOMapper.selectByPaperId(paperIds);
        if(CollectionUtils.isEmpty(refList)){
            return Set.of();
        }
        return refList.stream().map(PaperChapterReferenceVersionPO::getPaperId).collect(Collectors.toSet());
    }

    /**
     * 根据教材版本查询所有在章节中引用的试卷
     *
     * @param bookId 教材ID
     * @return 试卷引用列表
     */
    @Override
    public List<PaperReference> getPaperReferenceList(String bookId) {
        if (!StringUtils.hasText(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        // 查询引用中的试卷，按所属章节序号升序排列
        List<PaperChapterReferencePO> referenceList = paperChapterReferencePOMapper.selectLatestReferenceByBookId(bookId);
        if(CollectionUtils.isEmpty(referenceList)){
            log.debug("教材没有引用中的试卷，bookId：{}", bookId);
            return List.of();
        }

        return referenceList.stream().map(PaperChapterReferencePO::toEntity).toList();
    }
}
