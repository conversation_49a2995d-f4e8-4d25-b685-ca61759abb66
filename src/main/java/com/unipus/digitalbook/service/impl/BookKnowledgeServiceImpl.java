package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookKnowledgeInfoMapper;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeInfoPO;
import com.unipus.digitalbook.service.BookKnowledgeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 教材图谱相关接口
 */
@Service
public class BookKnowledgeServiceImpl implements BookKnowledgeService {

    @Resource
    BookKnowledgeInfoMapper bookKnowledgeInfoMapper;


    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    public int deleteByPrimaryKey(Long id) {
        return bookKnowledgeInfoMapper.deleteByPrimaryKey(id);
    }

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insert(BookKnowledgeInfoPO record) {
        return bookKnowledgeInfoMapper.insert(record);
    }

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insertSelective(BookKnowledgeInfoPO record) {
        return bookKnowledgeInfoMapper.insertSelective(record);
    }

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    public BookKnowledgeInfoPO selectByPrimaryKey(Long id) {
        return bookKnowledgeInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKeySelective(BookKnowledgeInfoPO record) {
        return bookKnowledgeInfoMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKey(BookKnowledgeInfoPO record) {
        return bookKnowledgeInfoMapper.updateByPrimaryKey(record);
    }


}
