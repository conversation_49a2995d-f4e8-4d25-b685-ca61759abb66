package com.unipus.digitalbook.service.remote.restful.engine.model;

import java.util.List;
import java.util.Map;

public class CorrectResult {
    private String ext;
    private List<Score> score;
    private String parser;
    private Map<String, Object> feature;
    private Object correct;
    private double time;
    private String id;

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public List<Score> getScore() {
        return score;
    }

    public void setScore(List<Score> score) {
        this.score = score;
    }

    public String getParser() {
        return parser;
    }

    public void setParser(String parser) {
        this.parser = parser;
    }

    public Map<String, Object> getFeature() {
        return feature;
    }

    public void setFeature(Map<String, Object> feature) {
        this.feature = feature;
    }

    public Object getCorrect() {
        return correct;
    }

    public void setCorrect(Object correct) {
        this.correct = correct;
    }

    public double getTime() {
        return time;
    }

    public void setTime(double time) {
        this.time = time;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "CorrectResult{" +
                "ext='" + ext + '\'' +
                ", score=" + score +
                ", parser='" + parser + '\'' +
                ", feature=" + feature +
                ", correct=" + correct +
                ", time=" + time +
                ", id='" + id + '\'' +
                '}';
    }
}
