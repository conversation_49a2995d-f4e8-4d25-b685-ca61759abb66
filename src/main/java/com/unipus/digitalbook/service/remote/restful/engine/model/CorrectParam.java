package com.unipus.digitalbook.service.remote.restful.engine.model;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.question.UserAnswer;

public class CorrectParam {
    /**
     * 标题
     */
    private String title;
    /**
     * 内容 必要字段
     */
    private String content;
    /**
     * 文章关键词，多个用“,”分割
     */
    private String keywords;
    /**
     * 文章字数下限
     */
    private Integer wordLimitMin;

    /**
     * 文章字数上限
     */
    private Integer wordLimitMax;

    /**
     * 请求ID，用于唯一标识一次请求，用于后续问题排查
     */
    private String requestId;
    /**
     * 文章提示标题
     */
    private String promptTitle;

    /**
     * 文章提示内容
     */
    private String promptContent;

    /**
     * 文章内容是否为base64编码
     */
    private boolean base64Enc;

    /**
     * 评测优先级：0普通，1VIP，3低优先级，默认为普通
     */
    private int fastVip;

    public CorrectParam() {}
    public CorrectParam(UserAnswer userAnswer, String questionText,  Integer wordLimit) {
        // 用户作答内容
        this.content = userAnswer.getAnswer();
        this.promptContent = questionText;
        this.requestId = IdentifierUtil.getShortUUID();
        this.wordLimitMax = wordLimit;
        this.base64Enc = false;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Integer getWordLimitMin() {
        return wordLimitMin;
    }

    public void setWordLimitMin(Integer wordLimitMin) {
        this.wordLimitMin = wordLimitMin;
    }

    public Integer getWordLimitMax() {
        return wordLimitMax;
    }

    public void setWordLimitMax(Integer wordLimitMax) {
        this.wordLimitMax = wordLimitMax;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPromptTitle() {
        return promptTitle;
    }

    public void setPromptTitle(String promptTitle) {
        this.promptTitle = promptTitle;
    }

    public String getPromptContent() {
        return promptContent;
    }

    public void setPromptContent(String promptContent) {
        this.promptContent = promptContent;
    }

    public boolean isBase64Enc() {
        return base64Enc;
    }

    public void setBase64Enc(boolean base64Enc) {
        this.base64Enc = base64Enc;
    }

    public int getFastVip() {
        return fastVip;
    }

    public void setFastVip(int fastVip) {
        this.fastVip = fastVip;
    }

    @Override
    public String toString() {
        return "CorrectParam{" +
                "title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", keywords='" + keywords + '\'' +
                ", wordLimitMin=" + wordLimitMin +
                ", wordLimitMax=" + wordLimitMax +
                ", requestId='" + requestId + '\'' +
                ", promptTitle='" + promptTitle + '\'' +
                ", promptContent='" + promptContent + '\'' +
                ", base64Enc=" + base64Enc +
                ", fastVip=" + fastVip +
                '}';
    }
}
