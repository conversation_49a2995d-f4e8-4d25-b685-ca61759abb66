package com.unipus.digitalbook.service.remote.restful.engine.model;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.List;

public class TranslateParam {
    private String src_lang;

    private String src_txt;

    private String tgt_lang;

    private String tgt_txt;

    private List<String> refs;

    private String token;

    private String appkey;

    /**
     * 秒时间戳
     */
    private String timestamp;

    public TranslateParam(){}

    public TranslateParam(String appKey, String appSecret,String srcLang, String srcTxt, String tgtLang, String tgtTxt, List<String> refs) {
        this.src_lang = srcLang;
        this.src_txt = srcTxt;
        this.tgt_lang = tgtLang;
        this.tgt_txt = tgtTxt;
        this.refs = refs;
        this.appkey = appKey;
        this.timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        this.token = DigestUtils.md5Hex(appKey+timestamp+appSecret);
    }

    public String getSrc_lang() {
        return src_lang;
    }

    public void setSrc_lang(String src_lang) {
        this.src_lang = src_lang;
    }

    public String getSrc_txt() {
        return src_txt;
    }

    public void setSrc_txt(String src_txt) {
        this.src_txt = src_txt;
    }

    public String getTgt_lang() {
        return tgt_lang;
    }

    public void setTgt_lang(String tgt_lang) {
        this.tgt_lang = tgt_lang;
    }

    public String getTgt_txt() {
        return tgt_txt;
    }

    public void setTgt_txt(String tgt_txt) {
        this.tgt_txt = tgt_txt;
    }

    public List<String> getRefs() {
        return refs;
    }

    public void setRefs(List<String> refs) {
        this.refs = refs;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
