package com.unipus.digitalbook.service.remote.restful.sso.response;

/**
 * @Author: 中文输入法发挥不稳定的刘川
 * @Date: 2024/10/18 上午11:31
 *
 * 结果类，用于封装API请求的结果信息。
 */
public class Result {

    private String user;

    private Attributes attributes;

    public String getUser() {
        return user;
    }

    public Result setUser(String user) {
        this.user = user;
        return this;
    }

    public Attributes getAttributes() {
        return attributes;
    }

    public Result setAttributes(Attributes attributes) {
        this.attributes = attributes;
        return this;
    }
}

