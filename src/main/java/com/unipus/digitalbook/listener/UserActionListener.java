package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentAction;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class UserActionListener {
    @Resource
    private UserContentActionFactory userContentActionFactory;

    @KafkaListener(topics = "${kafka.topic.userAction}")
    public void processMessage(String message) {
        log.info("received user action message: {}", message);
        UserActionEvent userActionEvent = JSON.parseObject(message, UserActionEvent.class);
        UserAction userAction = userActionEvent.toUserAction();
        UserContentAction contentAction = userContentActionFactory.getContentAction(userActionEvent.getContentType());
        if (contentAction == null) {
            log.error("received user action message is error : {}", message);
            return;
        }
        // 节点完成后的后置处理
        contentAction.postProcessNode(userAction, userActionEvent.getContentNode());
    }
}
