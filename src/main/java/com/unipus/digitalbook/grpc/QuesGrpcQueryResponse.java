package com.unipus.digitalbook.grpc;

import cn.unipus.qs.api.proto.client.model.QuesDetailPO;
import cn.unipus.qs.api.proto.client.model.QuesDetailResponsePO;
import com.unipus.digitalbook.common.exception.question.GrpcQuestionInvalidResponseException;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 题目查询返回实体类
 */
public class QuesGrpcQueryResponse implements Serializable {

    @Schema(description = "题目状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private QuesGrpcStatus status;

    @Schema(description = "题目信息")
    private List<QuesGrpcDetailResponse> quesGrpcDetails;

    public QuesGrpcStatus getStatus() {
        return status;
    }

    public void setStatus(QuesGrpcStatus status) {
        this.status = status;
    }

    public List<QuesGrpcDetailResponse> getQuesGrpcDetailResponse() {
        return quesGrpcDetails;
    }

    public void setQuesGrpcDetailResponse(List<QuesGrpcDetailResponse> quesGrpcDetailResponse) {
        this.quesGrpcDetails = quesGrpcDetailResponse;
    }

    public static QuesGrpcQueryResponse fromGrpcResponse(QuesDetailResponsePO response) {
        QuesGrpcQueryResponse quesGrpcQueryResponse = new QuesGrpcQueryResponse();
        if (response == null) {
            throw new GrpcQuestionInvalidResponseException("response is null.(QuesDetailResponsePO)");
        }

        List<QuesGrpcDetailResponse> quesGrpcDetailResponse = new java.util.ArrayList<>();
        for (QuesDetailPO quesDetailPO : response.getQuesDetailList()) {
            quesGrpcDetailResponse.add(QuesGrpcDetailResponse.build(quesDetailPO));
        }
        quesGrpcQueryResponse.setQuesGrpcDetailResponse(quesGrpcDetailResponse);
        quesGrpcQueryResponse.setStatus(new QuesGrpcStatus(response.getStatus().getCode(), response.getStatus().getMsg()));

        return quesGrpcQueryResponse;
    }

}
