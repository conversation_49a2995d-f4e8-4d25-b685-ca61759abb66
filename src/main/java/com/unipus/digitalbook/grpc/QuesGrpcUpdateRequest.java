package com.unipus.digitalbook.grpc;

import cn.unipus.qs.api.proto.client.model.QuesMetadataUpdatePO;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 题目信息参数实体类
 */
@Schema(description = "题目信息参数实体类")
public class QuesGrpcUpdateRequest extends QuesGrpcDetailBase {

    @Schema(description = "批改方式(更新时必须)", example = "single_choice")
    private String correctType = "";

    @Schema(description = "题目ID(更新时必须)")
    private Long quesId;

    @Schema(description = "题目版本(更新时必须)")
    private Integer quesVersion;

    public String getCorrectType() {
        return correctType;
    }

    public void setCorrectType(String correctType) {
        this.correctType = correctType;
    }

    public Long getQuesId() {
        return quesId;
    }

    public void setQuesId(Long quesId) {
        this.quesId = quesId;
    }

    public Integer getQuesVersion() {
        return quesVersion;
    }

    public void setQuesVersion(Integer quesVersion) {
        this.quesVersion = quesVersion;
    }

    public QuesGrpcUpdateRequest(BigQuestionGroup questionGroup, Long userId, Long quesId, Integer quesVersion, String correctType) {
        super(questionGroup, userId);
        this.correctType = correctType;
        this.quesId = quesId;
        this.quesVersion = quesVersion;
    }

    public QuesMetadataUpdatePO buildUpdatePO(){

        return QuesMetadataUpdatePO.newBuilder()
                .setQuesTypeId(this.getQuesTypeId())
                .setQuesTypeVersion(this.getQuesTypeVersion())
                .setBankId(this.getBankId())
                .setTitle(this.getTitle())
                .setDescription(this.getDescription())
                .setQuesDifficulty(Int32Value.newBuilder().setValue(this.getQuesDifficulty()).build())
                .setSourceSystemExternalId(this.getSourceSystemExternalId())
                .setSourceSystemExternalVersion(this.getSourceSystemExternalVersion())
                .setQuesYear(this.getQuesYear())
                .setSourceType(this.getSourceType())
                .setSubject(this.getSubject())
                .setShareStatus(this.getShareStatus())
                .setContent(this.getContent())
                .setOwner(Int64Value.newBuilder().setValue(this.getOwner()).build())
                .setSourceId(Int32Value.of(this.getSourceId()))
                .setUserId(this.getUserId())
                .setCorrectType(this.getCorrectType())
                .setQuesId(this.getQuesId())
                .setQuesVersion(this.getQuesVersion())
                .addAllTag(List.of())
                .build();
    }

}