package com.unipus.digitalbook.grpc.format;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 题目类别枚举
 */
@Getter
@AllArgsConstructor
public enum QuestionCategory {
    BASIC(1, "Basic","测验类"),
    STUDY(0, "Study", "学习类 没有作答"),
    HEARING(6, "Hearing","听力类"),
    ORAL(7, "Oral", "口语类"),
    WRITING(8, "Writing", "写作题");

    private final Integer value;
    private final String name;
    private final String description;

    public static Integer getValueByName(String name) {
        if(!StringUtils.hasText(name)){
            return null;
        }
        for (QuestionCategory questionCategory : QuestionCategory.values()) {
            if (questionCategory.getName().equals(name)) {
                return questionCategory.getValue();
            }
        }
        return null;
    }
}