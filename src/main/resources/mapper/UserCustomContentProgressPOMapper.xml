<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserCustomContentProgressPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="openId" column="open_id" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="contentId" column="content_id" jdbcType="VARCHAR"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,open_id,tenant_id,
        content_id,enable,create_time,
        update_time,progress_bit
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_custom_content_progress
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="getProgressList" resultMap="BaseResultMap">
        SELECT
            open_id,
            content_id,
        FROM user_chapter_progress
        WHERE tenant_id = #{tenantId}
          AND content_id = #{contentId}
          AND enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_custom_content_progress
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO" useGeneratedKeys="true">
        insert into user_custom_content_progress
        ( id,open_id,tenant_id
        ,content_id,enable,create_time
        ,update_time,progress_bit)
        values (#{id,jdbcType=BIGINT},#{openId,jdbcType=VARCHAR},#{tenantId,jdbcType=BIGINT}
        ,#{contentId,jdbcType=VARCHAR},#{enable,jdbcType=BIT},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP},#{progressBit,jdbcType=BLOB})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO" useGeneratedKeys="true">
        insert into user_custom_content_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="openId != null">open_id,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="contentId != null">content_id,</if>
                <if test="enable != null">enable,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="progressBit != null">progress_bit,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
                <if test="contentId != null">#{contentId,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="progressBit != null">#{progressBit,jdbcType=BLOB},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO">
        update user_custom_content_progress
        <set>
                <if test="openId != null">
                    open_id = #{openId,jdbcType=VARCHAR},
                </if>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=BIGINT},
                </if>
                <if test="contentId != null">
                    content_id = #{contentId,jdbcType=VARCHAR},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="progressBit != null">
                    progress_bit = #{progressBit,jdbcType=BLOB},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO">
        update user_custom_content_progress
        set 
            open_id =  #{openId,jdbcType=VARCHAR},
            tenant_id =  #{tenantId,jdbcType=BIGINT},
            content_id =  #{contentId,jdbcType=VARCHAR},
            enable =  #{enable,jdbcType=BIT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            progress_bit =  #{progressBit,jdbcType=BLOB}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <insert id="saveProgressBit">
        insert into user_chapter_progress
            (open_id,tenant_id,content_id,progress_bit)
        values (#{openId,jdbcType=VARCHAR},#{tenantId,jdbcType=BIGINT},#{contentId,jdbcType=VARCHAR},#{progressBit,jdbcType=BLOB})
            on duplicate key update
                                 progress_bit = #{progressBit,jdbcType=BLOB},
                                 update_time = now()
    </insert>
</mapper>
