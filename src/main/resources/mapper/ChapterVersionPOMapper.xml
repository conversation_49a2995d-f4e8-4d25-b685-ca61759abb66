<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.ChapterVersionPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="chapter_id" jdbcType="CHAR" property="chapterId" />
    <result column="version_number" jdbcType="VARCHAR" property="versionNumber" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="resource_json" jdbcType="LONGVARCHAR" property="resourceJson" />
    <result column="student_content" jdbcType="LONGVARCHAR" property="studentContent" />
    <result column="catalog_json" jdbcType="LONGVARCHAR" property="catalogJson" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="header_img" jdbcType="VARCHAR" property="headerImg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="total_struct" jdbcType="LONGVARCHAR" property="totalStruct" typeHandler="com.unipus.digitalbook.conf.mybatis.type.handler.ChapterNodePOListTypeHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--
    @mbg.generated
    -->
    id, chapter_id, version_number,name,header_img, create_time, update_time, create_by, update_by, `enable`
  </sql>
  <sql id="Blob_Column_List">
    <!--
    @mbg.generated
    -->
    content, total_struct
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
    @mbg.generated
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chapter_version
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectLatestVersionByChapterId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    ,
    student_content,
    catalog_json,
    resource_json
    from chapter_version
    where chapter_id = #{chapterId,jdbcType=CHAR}
    and `enable` = 1
    order by version_number desc
    limit 1
  </select>
    <select id="selectByChapterId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from chapter_version
      where chapter_id = #{chapterId,jdbcType=CHAR}
      and `enable` = 1

      order by version_number desc
    </select>
  <select id="selectLatestVersionByChapterIds" resultType="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO" resultMap="BaseResultMap">
      SELECT * FROM (
      SELECT *,
      ROW_NUMBER() OVER (PARTITION BY chapter_id ORDER BY version_number DESC) AS rn
      FROM chapter_version
      WHERE chapter_id IN
      <foreach collection="chapterIds" item="chapterId" open="(" separator="," close=")">
        #{chapterId,jdbcType=CHAR}
      </foreach>
      AND `enable` = 1
      ) latest
      WHERE latest.rn = 1
  </select>

  <select id="selectLatestVersionIdByChapterIds" resultType="java.lang.Long">
    SELECT id FROM (
    SELECT id,
    ROW_NUMBER() OVER (PARTITION BY chapter_id ORDER BY version_number DESC) AS rn
    FROM chapter_version
    WHERE chapter_id IN
    <foreach collection="chapterIds" item="chapterId" open="(" separator="," close=")">
      #{chapterId,jdbcType=CHAR}
    </foreach>
    AND `enable` = 1
    ) latest
    WHERE latest.rn = 1
  </select>

  <select id="selectByChapterIdAndVersionNumber" resultMap="ResultMapWithBLOBs">
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      ,
      student_content,
      catalog_json,
      resource_json
      from chapter_version
      where chapter_id = #{chapterId,jdbcType=CHAR}
      and version_number = #{versionNumber,jdbcType=VARCHAR}
      and `enable` = 1
  </select>

  <select id="selectIdByChapterIdAndVersionNumber" resultType="java.lang.Long">
    select id from chapter_version
    where enable = true
    and chapter_id = #{chapterId,jdbcType=CHAR}
    and version_number = #{versionNumber,jdbcType=VARCHAR}
  </select>

  <select id="selectLatestVersionIdByChapterId" resultType="java.lang.Long">
    select id from chapter_version
    where enable = true
    and chapter_id = #{chapterId,jdbcType=CHAR}
    order by version_number desc
    limit 1
  </select>
    <select id="selectVersionListByIdList"
            resultMap="ResultMapWithBLOBs">
      select * from chapter_version
      where id in
      <foreach collection="idList" item="id" open="(" separator="," close=")">
        #{id,jdbcType=BIGINT}
      </foreach>
      and `enable` = true

    </select>
  <select id="selectCatalogByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />, catalog_json
    from chapter_version
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
    and `enable` = true
  </select>
  <select id="selectResourceByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />, resource_json
    from chapter_version
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
    and `enable` = true
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
    @mbg.generated
    -->
    delete from chapter_version
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chapter_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="chapterId != null">
        chapter_id,
      </if>
      <if test="versionNumber != null">
        version_number,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="resourceJson != null">
        resource_json,
      </if>
      <if test="studentContent != null">
        student_content,
      </if>
      <if test="catalogJson != null">
        catalog_json,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="headerImg != null">
        header_img,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="totalStruct != null">
        total_struct,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="chapterId != null">
        #{chapterId,jdbcType=CHAR},
      </if>
      <if test="versionNumber != null">
        #{versionNumber,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="resourceJson != null">
        #{resourceJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="studentContent != null">
        #{studentContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="catalogJson != null">
        #{catalogJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="headerImg != null">
        #{headerImg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="totalStruct != null">
        #{totalStruct,typeHandler=com.unipus.digitalbook.conf.mybatis.type.handler.ChapterNodePOListTypeHandler}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    update chapter_version
    <set>
      <if test="chapterId != null">
        chapter_id = #{chapterId,jdbcType=CHAR},
      </if>
      <if test="versionNumber != null">
        version_number = #{versionNumber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    update chapter_version
    set chapter_id = #{chapterId,jdbcType=CHAR},
      version_number = #{versionNumber,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.chapter.ChapterVersionPO">
    <!--
    @mbg.generated
    -->
    update chapter_version
    set chapter_id = #{chapterId,jdbcType=CHAR},
      version_number = #{versionNumber,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectPageByChapterId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from chapter_version
    where chapter_id = #{chapterId,jdbcType=CHAR}
    and `enable` = 1
    order by version_number desc
    <if test="page != null">
      limit #{page.offset}, #{page.limit}
    </if>
  </select>

  <select id="countByChapterId" resultType="java.lang.Integer">
    select count(*)
    from chapter_version
    where chapter_id = #{chapterId,jdbcType=CHAR}
    and `enable` = 1
  </select>
  <select id="selectByBookVersionIdAndChapterId" resultMap="BaseResultMap">
    select c.id as id,
           c.chapter_id as chapter_id,
           c.content as content,
           c.resource_json as resource_json,
           c.catalog_json as catalog_json,
           c.student_content as student_content,
           c.version_number as version_number,
           c.name as name,
           c.header_img as header_img,
           c.`enable` as enable
    from chapter_version c
    join book_version_chapter_version_relation br on c.id = br.chapter_version_id
    where br.book_version_id = #{bookVersionId,jdbcType=BIGINT}
    and c.chapter_id = #{chapterId,jdbcType=CHAR}
    and c.`enable` = 1
  </select>
  <select id="selectChapterIdsByIds" resultMap="BaseResultMap">
    select id, chapter_id
    from chapter_version
    where id in
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectChapterContentByChapterId" resultMap="BaseResultMap">
    select
    id,
    chapter_id,
    version_number,
    content,
    student_content
    from chapter_version
    where chapter_id = #{chapterId,jdbcType=CHAR}
    and `enable` = 1
    order by version_number desc
  </select>
  <select id="selectChapterNodeByChapterVersionId" resultMap="ResultMapWithBLOBs">
    select
      total_struct
    from chapter_version where enable = true and id = #{chapterVersionId}
  </select>
  <select id="selectVersionIdByChapterIdAndVersionNumber" resultType="java.lang.Long">
    select id from chapter_version where enable = true and chapter_id = #{chapterId} and version_number = #{versionNumber}
  </select>

  <update id="updateChapterContentById">
    update chapter_version
    <set>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="studentContent != null">
        student_content = #{studentContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>