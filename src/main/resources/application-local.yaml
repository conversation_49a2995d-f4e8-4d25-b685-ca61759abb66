spring:
  datasource:
    url: *****************************************************************************************
    username: ipublish_user
    password: vuZGkiPgPDVGUdS4
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
  # kafka配置
  kafka:
    bootstrap-servers: ************:9092,************:9092,***********:9092
    consumer:
      group-id: dev-ipublish-group
      auto-offset-reset: earliest
      # 是否启用kafka消费者（其他环境默认开启）
      enable: false
  servlet:
    multipart:
      max-file-size: 20MB
# kafka topic配置
kafka:
  topic:
    # 后期需要修改
    operationLog: dev-ipublish-bookOperationLog
    bookOperationLog: dev-ipublish-operation-book
    chapterOperationLog: dev-ipublish-operation-chapter
    clioResult: dev_clio_result_2_ipublish
    bookPublishTopic: dev-ipublish-bookPublished
    userAction: dev-ipublish-userAction
    uaiResourcePublish: test_resource_publish
    tenantMessageSaveTopic: dev-ipublish-tenantMessageSave
    tenantMessageDeleteTopic: dev-ipublish-tenantMessageDelete
    customContent: dev-ipublish-custom-content-Published
app:
  env: dev

redisson:
    nodes:
      - redis-6d67dc5e-555f-42ea-b0e8-07d0ab1dafaf.cn-north-4.dcs.myhuaweicloud.com:6379
    max-redirects: 3
    connection-timeout: 1000
    read-timeout: 1000
    scan-interval: 30

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /doc/swagger-ui.html

remote:
  sso:
    url: https://utsso.unipus.cn
    serviceName: https://ipublish-test.unipus.cn/
  aigc:
    url: https://llm-api.unipus.cn
    sk: sk-slP14hxuX5kLqEjfEb86190bE4C340D79159567f7b688f9f
  qrcode:
    url: https://testucontent-cms.unipus.cn
  soe:
    #url: 	http://zt.unipus.cn/soe
    url: https://soetest.unipus.cn
    appKey: ipublish
    appSecret: EiBBCfPzEMHvLQnN
  engine:
    appKey: iPublish_dev
    appSecret: kPROBC2QRgzb
    host: https://engine-huawei-test.unipus.cn
  knowledge:
    host: http://************:30447/mindmap
    resourceLinkUrl: https://ucloud-test-hw.unipus.cn/api/tla/courseStudy/knowledge/link
  translate:
    appKey: iPublish
    appSecret: uZxZq5HaKmUiji04207yZmff30z9vg8A
  ucontent:
    host: http://ucontent-api-v3.ucontent-test:80
  assistant:
    host: http://assistant-api.ucontent-test:8080
    appKey: testOnly20240529
    appSecret: testOnly20240529testOnly20240529
  wordpractice:
    host: https://ucloud-test-hw.unipus.cn
    secret: mySuperSecretKeyWithAtLeast32CharactersLong
  feishu:
    tenantMessageKey: 2e275d7c-e546-44a5-928c-5e5a92b59055

grpc:
  server:
    question:
      host: mid-qs-api-test.api1024.cn
      port: 30080

logging:
  level:
    com.unipus: DEBUG
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd'T'HH:mm:ss.SSSXXX}}){faint} %clr(${LOG_LEVEL_PATTERN:%5p}) %clr(${PID:- }){magenta} %clr(%X{requestId}) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}"

jwt:
  secret: 5LiN5piv5ZCn5LiN5piv5ZCn77yM6L+Z5LmI54K55bel6LWE6L+Y5oOz5bmy5ZWl5ZGi
  # token过期时间（天）
  expiration: 30
  coSecret: 5LiN5piv5ZCn5LiN5piv5ZCn77yM6L+Z5LmI54K55bel6LWE6L+Y5oOz5bmy5ZWl5ZGi
  # token过期时间（天）
  coExpiration: 30

white-list:
  # 登录校验白名单
  security-urls:
    - /auth/validate
    - /auth/accessToken
    - /v3/api-docs/**
    - /doc/swagger-ui/**
    - /NVQW4YLHMVWWK3TU/**
    - /NVQW4YLHMVWWK3TU/health
    - /NVQW4YLHMVWWK3TU/health/**

  # 权限校验白名单
  permission-urls:
    - /**
    - /doc/convert-word-to-html
    - /cos/getCredential

  # 内网调用白名单
  internal-urls:
    - /backend/**

  # 内网IP网段
  internal-ip-ranges:
    - **********/16
    - ***********/16

#测试环境腾讯云cos配置
tencent:
  cos:
    secret-id: AKID1vd9XkCdrtJZeaLwKftujRzNekjW9aQ8
    secret-key: d5NUO5av53rTs43qRq47yb1EH9qLekcH
    bucket: ipublish-test-**********
    region: ap-beijing



# 飞书机器人配置
feishu:
  bot:
    # 飞书机器人Webhook地址
    webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/be54aa3d-e7f2-45d8-bbf7-693b91ed87e5
    # 飞书机器人密钥（用于签名验证）
    secret:
    # 是否启用飞书机器人
    enabled: true
    # 请求超时时间（毫秒）
    timeout: 10000