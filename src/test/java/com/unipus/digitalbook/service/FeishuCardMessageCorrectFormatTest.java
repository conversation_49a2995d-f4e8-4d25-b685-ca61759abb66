package com.unipus.digitalbook.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书卡片消息正确格式测试
 */
public class FeishuCardMessageCorrectFormatTest {
    
    @Test
    public void testCorrectCardMessageFormat() {
        System.out.println("=== 测试正确的卡片消息格式 ===");
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 构建正确的卡片消息格式 - 按照飞书官方示例
            FeishuCardMessage cardMessage = buildCorrectCardMessage();
            String json = mapper.writeValueAsString(cardMessage);
            System.out.println("卡片消息JSON格式:");
            System.out.println(json);
            System.out.println();
            
            // 构建完整的消息格式
            FeishuMessage message = new FeishuMessage();
            message.setMsgType("interactive");
            // 卡片消息使用顶级的card字段
            message.setCard(cardMessage.getCard());
            String messageJson = mapper.writeValueAsString(message);
            System.out.println("完整消息JSON格式:");
            System.out.println(messageJson);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private FeishuCardMessage buildCorrectCardMessage() {
        FeishuCardMessage cardMessage = new FeishuCardMessage();
        FeishuCardMessage.CardContent cardContent = new FeishuCardMessage.CardContent();
        
        // 设置卡片元素
        List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
        
        // 添加文本元素 - 按照飞书官方示例格式
        FeishuCardMessage.CardElement textElement = new FeishuCardMessage.CardElement();
        textElement.setTag("div");
        FeishuCardMessage.CardText cardText = new FeishuCardMessage.CardText();
        cardText.setContent("at所有人<at id=all></at> \n at指定人<at id=ou_xxxxxx></at>");
        cardText.setTag("lark_md");
        textElement.setText(cardText);
        elements.add(textElement);
        
        cardContent.setElements(elements);
        cardMessage.setCard(cardContent);
        
        return cardMessage;
    }
    
    // 简化的消息类
    static class FeishuMessage {
        private String msgType;
        private Object content;
        private Object card;
        
        public String getMsgType() { return msgType; }
        public void setMsgType(String msgType) { this.msgType = msgType; }
        public Object getContent() { return content; }
        public void setContent(Object content) { this.content = content; }
        public Object getCard() { return card; }
        public void setCard(Object card) { this.card = card; }
    }
} 